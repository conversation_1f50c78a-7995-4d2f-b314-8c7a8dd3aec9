import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../menu/presentation/widgets/side_menu_drawer.dart';
import '../../../../core/constants/app_shadows.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../favorites/presentation/pages/favorites_page.dart';
import '../../../cart/presentation/pages/cart_page.dart';
import '../../../stores/presentation/pages/stores_page.dart';
import '../../../profile/presentation/pages/profile_page.dart';

class SportyCornerApp extends StatelessWidget {
  const SportyCornerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Sporty Corner',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: GoogleFonts.cairo().fontFamily,
        scaffoldBackgroundColor: AppColors.background,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: IconThemeData(color: AppColors.primary),
          titleTextStyle: TextStyle(
            color: AppColors.primary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    // إضافة مستمع لتحديث الفهرس عند تغيير التبويب
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // دوال التنقل للأقسام المختلفة
  void _navigateToFeaturedStores() {
    Navigator.pushNamed(context, AppRoutes.featuredStores);
  }

  void _navigateToSportsClubs() {
    Navigator.pushNamed(context, AppRoutes.stores);
  }

  void _navigateToFeaturedProducts() {
    Navigator.pushNamed(context, AppRoutes.featuredProducts);
  }

  void _navigateToTopRatedProducts() {
    Navigator.pushNamed(context, AppRoutes.topRatedProducts);
  }

  void _navigateToSelectedProducts() {
    Navigator.pushNamed(context, AppRoutes.selectedProducts);
  }

  void _navigateToDiscountProducts() {
    Navigator.pushNamed(context, AppRoutes.discountProducts);
  }

  void _navigateToGeneralProducts() {
    Navigator.pushNamed(context, AppRoutes.generalProducts);
  }

  void _navigateToNotifications() {
    Navigator.pushNamed(context, AppRoutes.notifications);
  }

  void _navigateToPromotionalOffer() {
    Navigator.pushNamed(context, AppRoutes.discountProducts);
  }

  void _navigateToSearch() {
    Navigator.pushNamed(context, AppRoutes.search);
  }

  void _navigateToProfile() {
    _onTabTapped(4); // Navigate to profile tab
  }

  void _navigateToCart() {
    _onTabTapped(3); // Navigate to cart tab
  }

  void _navigateToFavorites() {
    _onTabTapped(1); // Navigate to favorites tab
  }

  void _shareProduct(String productName) {
    // Share product functionality
    // You can use the share_plus package here
    print('مشاركة المنتج: $productName');
  }

  void _toggleFavorite(String productName) {
    // Toggle favorite functionality
    print('إضافة/إزالة من المفضلة: $productName');
  }

  void _addToCart(String productName) {
    // Add to cart functionality
    print('إضافة للسلة: $productName');
    // Show snackbar to confirm addition
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم إضافة $productName للسلة',
          style: GoogleFonts.cairo(),
          textAlign: TextAlign.right,
        ),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _tabController.animateTo(index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.background,
      drawer: const SideMenuDrawer(),
      body: SafeArea(
        child: TabBarView(
          controller: _tabController,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _buildHomeTab(),
            _buildFavoritesTab(),
            _buildStoresTab(),
            _buildCartTab(),
            _buildProfileTab(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  // دوال التبويبات
  Widget _buildHomeTab() {
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildSearchBar(),
                _buildPromotionalBanner(),
                _buildPageIndicator(),
                _buildFeaturedStoresSection(),
                _buildSportsClubsSection(),
                _buildFeaturedProductsSection(),
                _buildTopRatedProductsSection(),
                _buildSelectedForYouSection(),
                _buildDiscountProductsSection(),
                _buildGeneralProductsSection(),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFavoritesTab() {
    return const FavoritesPage();
  }

  Widget _buildStoresTab() {
    return const StoresPage();
  }

  Widget _buildCartTab() {
    return const CartPage();
  }

  Widget _buildProfileTab() {
    return const ProfilePage();
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.defaultPadding,
        vertical: AppDimensions.defaultPadding / 2,
      ),
      color: Colors.white,
      child: Row(
        children: [
          // Menu and notification icons
          Row(
            children: [
              IconButton(
                onPressed: () {
                  _scaffoldKey.currentState?.openDrawer();
                },
                icon: const Icon(
                  Icons.menu,
                  color: AppColors.primary,
                  size: 28,
                ),
              ),
              Stack(
                children: [
                  IconButton(
                    onPressed: _navigateToNotifications,
                    icon: const Icon(
                      Icons.notifications_outlined,
                      color: AppColors.primary,
                      size: 28,
                    ),
                  ),
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: const Text(
                        '4',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const Spacer(),
          // Welcome text
          GestureDetector(
            onTap: _navigateToProfile,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'مرحبا ',
                    style: GoogleFonts.cairo(
                      color: AppColors.textPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: 'Heba Shaheen',
                    style: GoogleFonts.cairo(
                      color: AppColors.primary,
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.defaultPadding),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) => const FilterBottomSheet(),
              );
            },
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(12),
                boxShadow: AppShadows.light,
              ),
              child: const Icon(
                Icons.filter_list,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: AppShadows.light,
              ),
              child: TextField(
                textAlign: TextAlign.right,
                decoration: InputDecoration(
                  hintText: 'ما الذي تبحث عنه ؟',
                  hintStyle: GoogleFonts.cairo(
                    color: AppColors.textSecondary,
                    fontSize: 15,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  suffixIcon: IconButton(
                    icon: const Icon(
                      Icons.search,
                      color: AppColors.textSecondary,
                      size: 28,
                    ),
                    onPressed: _navigateToSearch,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionalBanner() {
    return GestureDetector(
      onTap: _navigateToPromotionalOffer,
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultPadding,
        ),
        height: 180,
        decoration: BoxDecoration(
          color: AppColors.primaryOrange,
          borderRadius: BorderRadius.circular(
            AppDimensions.cardBorderRadius * 2,
          ),
          boxShadow: AppShadows.medium,
        ),
        child: Stack(
          children: [
            Positioned.fill(
              child: Align(
                alignment: Alignment.centerRight,
                child: Icon(
                  Icons.sports_soccer,
                  size: 180,
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
            ),
            // Soccer ball icon positioned on the left
            Positioned(
              left: -40,
              top: 20,
              bottom: 20,
              child: Icon(
                Icons.sports_soccer,
                size: 140,
                color: Colors.white.withValues(alpha: 0.15),
              ),
            ),
            Positioned(
              right: 20,
              top: 30,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'تسوق كل ما يخص كرة القدم',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أفضل المنتجات الرياضية بأسعار مميزة',
                    style: GoogleFonts.cairo(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _navigateToPromotionalOffer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 28,
                        vertical: 14,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 3,
                    ),
                    child: Text(
                      'تسوق الآن',
                      style: GoogleFonts.cairo(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 40,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 10),
          Container(
            width: 10,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 10),
          Container(
            width: 10,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, VoidCallback onViewAllPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.defaultPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: onViewAllPressed,
            child: Text('عرض الكل', style: AppTextStyles.viewAllLink),
          ),
          Text(title, style: AppTextStyles.sectionHeader),
        ],
      ),
    );
  }

  Widget _buildFeaturedStoresSection() {
    return Column(
      children: [
        _buildSectionHeader('متاجر مميزة', _navigateToFeaturedStores),
        const SizedBox(height: 16),
        SizedBox(
          height: 250,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getStoreLogos().length,
            itemBuilder: (context, index) {
              return _buildStoreCard(_getStoreLogos()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSportsClubsSection() {
    return Column(
      children: [
        _buildSectionHeader('نوادي رياضية', _navigateToSportsClubs),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getTopClubs().length,
            itemBuilder: (context, index) {
              return _buildClubLogo(_getTopClubs()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildFeaturedProductsSection() {
    return Column(
      children: [
        _buildSectionHeader('منتجات مميزة', _navigateToFeaturedProducts),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getSportsProducts().length,
            itemBuilder: (context, index) {
              return _buildProductCard(_getSportsProducts()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildTopRatedProductsSection() {
    return Column(
      children: [
        _buildSectionHeader('منتجات الاعلى تقييم', _navigateToTopRatedProducts),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getSportsProducts().length,
            itemBuilder: (context, index) {
              return _buildProductCard(_getSportsProducts()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSelectedForYouSection() {
    return Column(
      children: [
        _buildSectionHeader('منتجات مختارة لك', _navigateToSelectedProducts),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getSportsProducts().length,
            itemBuilder: (context, index) {
              return _buildProductCard(_getSportsProducts()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildDiscountProductsSection() {
    return Column(
      children: [
        _buildSectionHeader(
          'منتجات خصم أكثر من 40 % لك',
          _navigateToDiscountProducts,
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getSportsProducts().length,
            itemBuilder: (context, index) {
              return _buildProductCard(_getSportsProducts()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildGeneralProductsSection() {
    return Column(
      children: [
        _buildSectionHeader('المنتجات العامة', _navigateToGeneralProducts),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultPadding,
            ),
            itemCount: _getSportsProducts().length,
            itemBuilder: (context, index) {
              return _buildProductCard(_getSportsProducts()[index]);
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  // قائمة الأندية العالمية العشرة الأولى
  List<Map<String, String>> _getTopClubs() {
    return [
      {
        'name': 'ريال مدريد',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png',
      },
      {
        'name': 'برشلونة',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Barcelona-Logo.png',
      },
      {
        'name': 'مانشستر يونايتد',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png',
      },
      {
        'name': 'ليفربول',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png',
      },
      {
        'name': 'مانشستر سيتي',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Manchester-City-Logo.png',
      },
      {
        'name': 'تشيلسي',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Chelsea-Logo.png',
      },
      {
        'name': 'أرسنال',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Arsenal-Logo.png',
      },
      {
        'name': 'بايرن ميونخ',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png',
      },
      {
        'name': 'يوفنتوس',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Juventus-Logo.png',
      },
      {
        'name': 'باريس سان جيرمان',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/06/Paris-Saint-Germain-PSG-Logo.png',
      },
    ];
  }

  Widget _buildClubLogo(Map<String, String> club) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, AppRoutes.stores, arguments: club['name']);
      },
      child: Container(
        width: 100,
        margin: const EdgeInsets.only(left: 16),
        child: Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: AppShadows.light,
              ),
              child: ClipOval(
                child: Image.network(
                  club['logo']!,
                  fit: BoxFit.contain,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return const Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppColors.primary,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.sports_soccer,
                        color: Colors.grey,
                        size: 40,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              club['name']!,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // قائمة المتاجر الرياضية
  List<Map<String, String>> _getStoreLogos() {
    return [
      {
        'name': 'نايكي',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png',
        'rating': '4.8',
      },
      {
        'name': 'أديداس',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png',
        'rating': '4.7',
      },
      {
        'name': 'بوما',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Puma-Logo.png',
        'rating': '4.6',
      },
      {
        'name': 'أندر آرمور',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/09/Under-Armour-Logo.png',
        'rating': '4.5',
      },
      {
        'name': 'ريبوك',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Reebok-Logo.png',
        'rating': '4.4',
      },
    ];
  }

  Widget _buildStoreCard(Map<String, String> store) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          AppRoutes.storeDetail,
          arguments: store['name'],
        );
      },
      child: Container(
        width: 250,
        margin: const EdgeInsets.only(left: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            AppDimensions.cardBorderRadius * 1.5,
          ),
          boxShadow: AppShadows.medium,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(AppDimensions.cardBorderRadius * 1.5),
                ),
              ),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Image.network(
                    store['logo']!,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.primary,
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.white,
                        child: const Icon(
                          Icons.store,
                          color: Colors.grey,
                          size: 40,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    store['name']!,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 18),
                          const SizedBox(width: 4),
                          Text(
                            store['rating']!,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Text(
                          'معتمد',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // قائمة المنتجات الرياضية
  List<Map<String, String>> _getSportsProducts() {
    return [
      {
        'name': 'حذاء كرة قدم نايكي',
        'image':
            'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop&crop=center',
        'price': '\$89.99',
        'rating': '4.8',
      },
      {
        'name': 'كرة قدم أديداس',
        'image':
            'https://images.unsplash.com/photo-1614632537190-23e4b21577de?w=400&h=400&fit=crop&crop=center',
        'price': '\$29.99',
        'rating': '4.7',
      },
      {
        'name': 'قميص ريال مدريد',
        'image':
            'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center',
        'price': '\$79.99',
        'rating': '4.9',
      },
      {
        'name': 'شورت رياضي',
        'image':
            'https://images.unsplash.com/photo-1506629905607-d9d36b17d4a5?w=400&h=400&fit=crop&crop=center',
        'price': '\$34.99',
        'rating': '4.6',
      },
      {
        'name': 'حقيبة رياضية',
        'image':
            'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop&crop=center',
        'price': '\$49.99',
        'rating': '4.5',
      },
      {
        'name': 'قفازات حارس مرمى',
        'image':
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop&crop=center',
        'price': '\$39.99',
        'rating': '4.7',
      },
      {
        'name': 'ساعة رياضية ذكية',
        'image':
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop&crop=center',
        'price': '\$199.99',
        'rating': '4.8',
      },
      {
        'name': 'زجاجة مياه رياضية',
        'image':
            'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
        'price': '\$19.99',
        'rating': '4.4',
      },
    ];
  }

  Widget _buildProductCard(Map<String, String> product) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          AppRoutes.productDetail,
          arguments: product['name'],
        );
      },
      child: Container(
        width: 180,
        margin: const EdgeInsets.only(left: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            AppDimensions.productCardBorderRadius * 1.5,
          ),
          boxShadow: AppShadows.medium,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              height: 140,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(
                    AppDimensions.productCardBorderRadius * 1.5,
                  ),
                ),
                image: DecorationImage(
                  image: NetworkImage(product['image']!),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () => _toggleFavorite(product['name']!),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.favorite_border,
                          size: 20,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    left: 8,
                    child: GestureDetector(
                      onTap: () => _shareProduct(product['name']!),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.share,
                          size: 20,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 8,
                    left: 8,
                    child: GestureDetector(
                      onTap: () => _addToCart(product['name']!),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.add_shopping_cart,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    product['name']!,
                    style: GoogleFonts.cairo(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    textAlign: TextAlign.right,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 18),
                          const SizedBox(width: 4),
                          Text(
                            product['rating']!,
                            style: GoogleFonts.cairo(
                              fontSize: 13,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        product['price']!,
                        style: GoogleFonts.cairo(
                          fontSize: 17,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return SafeArea(
      child: Container(
        height: 70 + MediaQuery.of(context).padding.bottom,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: AppShadows.medium,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildBottomNavItem(
              icon: Icons.person_outline,
              label: 'الحساب',
              isActive: _currentIndex == 4,
              onTap: () => _onTabTapped(4),
            ),
            _buildBottomNavItem(
              icon: Icons.shopping_cart_outlined,
              label: 'سلة التسوق',
              isActive: _currentIndex == 3,
              onTap: () => _onTabTapped(3),
            ),
            _buildBottomNavItem(
              icon: Icons.store_outlined,
              label: 'المتاجر',
              isActive: _currentIndex == 2,
              onTap: () => _onTabTapped(2),
            ),
            _buildBottomNavItem(
              icon: Icons.favorite_outline,
              label: 'المفضلة',
              isActive: _currentIndex == 1,
              onTap: () => _onTabTapped(1),
            ),
            _buildBottomNavItem(
              icon: Icons.home,
              label: 'الرئيسية',
              isActive: _currentIndex == 0,
              onTap: () => _onTabTapped(0),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavItem({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        customBorder: const CircleBorder(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isActive ? AppColors.primary : AppColors.textSecondary,
              size: 26,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 11,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                color: isActive ? AppColors.primary : AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// نافذة الفلتر الاحترافية
class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({super.key});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  String selectedSortBy = 'الاعلى تقييماً';
  String selectedCategory = 'منتجات مميزة';
  List<String> selectedGenders = [];
  List<String> selectedSports = [];
  String selectedDiscount = '20%';
  String selectedPriceRange = '100 - 200';
  String selectedRating = '5';
  String selectedCountry = 'عرض الكل';

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildSortBySection(),
                  const SizedBox(height: 24),
                  _buildCategorySection(),
                  const SizedBox(height: 24),
                  _buildGenderSection(),
                  const SizedBox(height: 24),
                  _buildSportsSection(),
                  const SizedBox(height: 24),
                  _buildDiscountSection(),
                  const SizedBox(height: 24),
                  _buildPriceSection(),
                  const SizedBox(height: 24),
                  _buildRatingSection(),
                  const SizedBox(height: 24),
                  _buildCountrySection(),
                ],
              ),
            ),
          ),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: AppShadows.light,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, color: AppColors.textSecondary),
          ),
          Text('فلتر البحث', style: AppTextStyles.titleLarge),
          const SizedBox(width: 48), // للتوازن
        ],
      ),
    );
  }

  Widget _buildSortBySection() {
    return _buildFilterSection(
      title: 'مرتب حسب',
      child: _buildDropdownField(
        value: selectedSortBy,
        items: ['الاعلى تقييماً', 'الاقل سعراً', 'الاعلى سعراً', 'الاحدث'],
        onChanged: (value) => setState(() => selectedSortBy = value!),
      ),
    );
  }

  Widget _buildCategorySection() {
    return _buildFilterSection(
      title: 'الاقسام',
      child: _buildDropdownField(
        value: selectedCategory,
        items: ['منتجات مميزة', 'منتجات عامة', 'منتجات خصم', 'منتجات مختارة'],
        onChanged: (value) => setState(() => selectedCategory = value!),
      ),
    );
  }

  Widget _buildGenderSection() {
    return _buildFilterSection(
      title: 'عرض',
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildCheckboxColumn(
            'عرض الكل',
            selectedGenders.contains('عرض الكل'),
          ),
          _buildCheckboxColumn('رجالي', selectedGenders.contains('رجالي')),
          _buildCheckboxColumn('نسائي', selectedGenders.contains('نسائي')),
        ],
      ),
    );
  }

  Widget _buildSportsSection() {
    return _buildFilterSection(
      title: 'الرياضة',
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildCheckboxColumn('عرض الكل', selectedSports.contains('عرض الكل')),
          _buildCheckboxColumn('كرة قدم', selectedSports.contains('كرة قدم')),
          _buildCheckboxColumn('كرة سلة', selectedSports.contains('كرة سلة')),
        ],
      ),
    );
  }

  Widget _buildDiscountSection() {
    return _buildFilterSection(
      title: 'الخصم',
      child: _buildDropdownField(
        value: selectedDiscount,
        items: ['20%', '30%', '40%', '50%'],
        onChanged: (value) => setState(() => selectedDiscount = value!),
      ),
    );
  }

  Widget _buildPriceSection() {
    return _buildFilterSection(
      title: 'السعر',
      child: _buildDropdownField(
        value: selectedPriceRange,
        items: ['50 - 100', '100 - 200', '200 - 500', '500+'],
        onChanged: (value) => setState(() => selectedPriceRange = value!),
      ),
    );
  }

  Widget _buildRatingSection() {
    return _buildFilterSection(
      title: 'التقييم',
      child: _buildDropdownField(
        value: selectedRating,
        items: ['0', '1', '2', '3', '4', '5'],
        onChanged: (value) => setState(() => selectedRating = value!),
      ),
    );
  }

  Widget _buildCountrySection() {
    return _buildFilterSection(
      title: 'الدولة',
      child: _buildDropdownField(
        value: selectedCountry,
        items: ['عرض الكل', 'السعودية', 'الإمارات', 'مصر', 'الكويت'],
        onChanged: (value) => setState(() => selectedCountry = value!),
      ),
    );
  }

  // دوال مساعدة لبناء عناصر الفلتر
  Widget _buildFilterSection({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(title, style: AppTextStyles.titleMedium),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  Widget _buildDropdownField({
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          isExpanded: true,
          items:
              items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: AppTextStyles.bodyMedium,
                    textAlign: TextAlign.right,
                  ),
                );
              }).toList(),
          onChanged: onChanged,
          icon: const Icon(
            Icons.keyboard_arrow_down,
            color: AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildCheckboxColumn(String title, bool isSelected) {
    return Expanded(
      child: Column(
        children: [
          Checkbox(
            value: isSelected,
            onChanged: (value) {
              setState(() {
                if (title == 'عرض الكل') {
                  if (value == true) {
                    selectedGenders.clear();
                    selectedGenders.add('عرض الكل');
                    selectedSports.clear();
                    selectedSports.add('عرض الكل');
                  } else {
                    selectedGenders.remove('عرض الكل');
                    selectedSports.remove('عرض الكل');
                  }
                } else {
                  if (value == true) {
                    if (selectedGenders.contains(title) ||
                        selectedSports.contains(title)) {
                      return;
                    }
                    selectedGenders.remove('عرض الكل');
                    selectedSports.remove('عرض الكل');
                    if (title == 'رجالي' || title == 'نسائي') {
                      selectedGenders.add(title);
                    } else {
                      selectedSports.add(title);
                    }
                  } else {
                    selectedGenders.remove(title);
                    selectedSports.remove(title);
                  }
                }
              });
            },
            activeColor: AppColors.primary,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppShadows.light,
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                // إعادة تعيين الفلاتر
                setState(() {
                  selectedSortBy = 'الاعلى تقييماً';
                  selectedCategory = 'منتجات مميزة';
                  selectedGenders.clear();
                  selectedSports.clear();
                  selectedDiscount = '20%';
                  selectedPriceRange = '100 - 200';
                  selectedRating = '5';
                  selectedCountry = 'عرض الكل';
                });
              },
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'إعادة تعيين',
                style: AppTextStyles.buttonTextSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                // تطبيق الفلاتر
                Navigator.pop(context, {
                  'sortBy': selectedSortBy,
                  'category': selectedCategory,
                  'genders': selectedGenders,
                  'sports': selectedSports,
                  'discount': selectedDiscount,
                  'priceRange': selectedPriceRange,
                  'rating': selectedRating,
                  'country': selectedCountry,
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('تطبيق الفلتر', style: AppTextStyles.buttonText),
            ),
          ),
        ],
      ),
    );
  }
}
