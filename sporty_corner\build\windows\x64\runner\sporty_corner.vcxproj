﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CF62C22B-53F2-3AE8-9D57-2A35A2E517E3}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>sporty_corner</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">sporty_corner.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">sporty_corner</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">sporty_corner.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">sporty_corner</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">sporty_corner.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">sporty_corner</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\cloud_firestore\Debug\cloud_firestore_plugin.lib;..\plugins\firebase_auth\Debug\firebase_auth_plugin.lib;..\plugins\firebase_core\Debug\firebase_core_plugin.lib;..\plugins\firebase_storage\Debug\firebase_storage_plugin.lib;..\plugins\share_plus\Debug\share_plus_plugin.lib;..\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_firestore.lib;shell32.lib;Bcrypt.lib;DbgHelp.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_auth.lib;..\plugins\firebase_core\Debug\firebase_core_plugin.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_app.lib;icu.lib;..\flutter\Debug\flutter_wrapper_plugin.lib;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\flutter_windows.dll.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_storage.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/Debug/sporty_corner.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/Debug/sporty_corner.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\cloud_firestore\Profile\cloud_firestore_plugin.lib;..\plugins\firebase_auth\Profile\firebase_auth_plugin.lib;..\plugins\firebase_core\Profile\firebase_core_plugin.lib;..\plugins\firebase_storage\Profile\firebase_storage_plugin.lib;..\plugins\share_plus\Profile\share_plus_plugin.lib;..\plugins\url_launcher_windows\Profile\url_launcher_windows_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_firestore.lib;shell32.lib;Bcrypt.lib;DbgHelp.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_auth.lib;..\plugins\firebase_core\Profile\firebase_core_plugin.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_app.lib;icu.lib;..\flutter\Profile\flutter_wrapper_plugin.lib;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\flutter_windows.dll.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_storage.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/Profile/sporty_corner.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/Profile/sporty_corner.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\windows;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_auth\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_storage\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\cloud_firestore\Release\cloud_firestore_plugin.lib;..\plugins\firebase_auth\Release\firebase_auth_plugin.lib;..\plugins\firebase_core\Release\firebase_core_plugin.lib;..\plugins\firebase_storage\Release\firebase_storage_plugin.lib;..\plugins\share_plus\Release\share_plus_plugin.lib;..\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_firestore.lib;shell32.lib;Bcrypt.lib;DbgHelp.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_auth.lib;..\plugins\firebase_core\Release\firebase_core_plugin.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_app.lib;icu.lib;..\flutter\Release\flutter_wrapper_plugin.lib;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\flutter_windows.dll.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_storage.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/Release/sporty_corner.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/Release/sporty_corner.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/My Apps/Abdellah Apps/sporty_corner/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/My Apps/Abdellah Apps/sporty_corner/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/My Apps/Abdellah Apps/sporty_corner/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\flutter_window.cpp" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\main.cpp" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\utils.cpp" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\win32_window.cpp" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{CB9E5E54-DBB3-340A-B718-3B24DB49E713}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\cloud_firestore_plugin.vcxproj">
      <Project>{258F4BFA-456A-3C04-8A4F-6DC1BA79B08D}</Project>
      <Name>cloud_firestore_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\firebase_auth\firebase_auth_plugin.vcxproj">
      <Project>{7D3546A4-C324-3A11-A115-08A4E1F3DBD0}</Project>
      <Name>firebase_auth_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\firebase_core\firebase_core_plugin.vcxproj">
      <Project>{C784BE17-A647-30F7-A11F-67B5C2AC9115}</Project>
      <Name>firebase_core_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\firebase_storage\firebase_storage_plugin.vcxproj">
      <Project>{57E9F1E6-BEB5-3E82-B9EE-650FFF2FB69D}</Project>
      <Name>firebase_storage_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{4A215B23-B748-301A-83D7-788AA1E94AB9}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{9B5655D4-6C5F-3200-989F-79D73D115C9D}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{4926F3B9-348F-323F-83BC-1B219A412E7E}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\share_plus\share_plus_plugin.vcxproj">
      <Project>{3E8004D6-857B-3DBA-878B-ECAB2FD2F10C}</Project>
      <Name>share_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\url_launcher_windows\url_launcher_windows_plugin.vcxproj">
      <Project>{636B57B4-5BE1-3B27-A3DD-8D5C4A7FFBFE}</Project>
      <Name>url_launcher_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>