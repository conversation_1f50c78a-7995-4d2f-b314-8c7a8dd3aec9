import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/unified_card.dart';

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<Map<String, dynamic>> favoriteProducts = [
    {
      'id': '1',
      'name': 'حذاء Nike Air Max',
      'price': 120.00,
      'rating': 4.8,
      'image':
          'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff3-46b4-949b-2d16af2ad421/custom-nike-dunk-high-by-you-shoes.png',
      'isFavorite': true,
      'brand': 'Nike',
      'category': 'أحذية رياضية',
    },
    {
      'id': '2',
      'name': 'كرة قدم أديداس',
      'price': 45.00,
      'rating': 4.9,
      'image':
          'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8dbf2fcdf1b54b5b9a05af4d00826e47_9366/UCL_Pro_Ball_White_HT2486_01_standard.jpg',
      'isFavorite': true,
      'brand': 'Adidas',
      'category': 'كرات رياضية',
    },
    {
      'id': '3',
      'name': 'قميص ريال مدريد',
      'price': 85.00,
      'rating': 4.7,
      'image':
          'https://shop.realmadrid.com/_next/image?url=https%3A%2F%2Flegends.broadleafcloud.com%2Fapi%2Fasset%2Fcontent%2FRMCFMZ0165-01.jpg%3FcontextRequest%3D%257B%2522forceCatalogForFetch%2522%3Afalse%2C%2522applicationId%2522%3A%252201H4RD9NXMKQBQ1WVKM1181VD8%2522%2C%2522tenantId%2522%3A%2522REAL_MADRID%2522%257D&w=1920&q=75',
      'isFavorite': true,
      'brand': 'Real Madrid',
      'category': 'ملابس رياضية',
    },
  ];

  final List<Map<String, dynamic>> favoriteStores = [
    {
      'id': '1',
      'name': 'Nike Store',
      'rating': 4.9,
      'logo':
          'https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png',
      'isFavorite': true,
      'description': 'متجر Nike الرسمي للمنتجات الرياضية',
      'productsCount': 250,
    },
    {
      'id': '2',
      'name': 'Adidas Official',
      'rating': 4.8,
      'logo':
          'https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png',
      'isFavorite': true,
      'description': 'متجر Adidas الرسمي',
      'productsCount': 180,
    },
    {
      'id': '3',
      'name': 'Real Madrid Store',
      'rating': 4.7,
      'logo':
          'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png',
      'isFavorite': true,
      'description': 'المتجر الرسمي لنادي ريال مدريد',
      'productsCount': 95,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primary,
              indicatorWeight: 3,
              labelStyle: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.normal,
              ),
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.shopping_bag_outlined, size: 18),
                      const SizedBox(width: 8),
                      Text('المنتجات (${favoriteProducts.length})'),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.store_outlined, size: 18),
                      const SizedBox(width: 8),
                      Text('المتاجر (${favoriteStores.length})'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [_buildProductsTab(), _buildStoresTab()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsTab() {
    if (favoriteProducts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد منتجات مفضلة',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 20,
      ),
      itemCount: favoriteProducts.length,
      itemBuilder: (context, index) {
        final product = favoriteProducts[index];
        return _buildProductCard(product);
      },
    );
  }

  Widget _buildStoresTab() {
    if (favoriteStores.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store, size: 64, color: AppColors.textSecondary),
            SizedBox(height: 16),
            Text(
              'لا توجد متاجر مفضلة',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: favoriteStores.length,
      itemBuilder: (context, index) {
        final store = favoriteStores[index];
        return _buildStoreCard(store);
      },
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return ProductCard(
      name: product['name'],
      image: product['image'],
      price: '\$${product['price'].toStringAsFixed(0)}',
      rating: product['rating'].toDouble(),
      brand: product['brand'],
      isFavorite: product['isFavorite'] ?? true,
      onTap: () {
        Navigator.pushNamed(context, '/product-detail', arguments: product);
      },
      onFavoriteToggle: () {
        setState(() {
          product['isFavorite'] = !product['isFavorite'];
          if (!product['isFavorite']) {
            favoriteProducts.removeWhere((p) => p['id'] == product['id']);
          }
        });
      },
      onAddToCart: () {
        // Add to cart
      },
    );
  }

  Widget _buildStoreCard(Map<String, dynamic> store) {
    return StoreCard(
      name: store['name'],
      logo: store['logo'],
      rating: store['rating'].toDouble(),
      description: store['description'],
      productsCount: store['productsCount'],
      isVerified: true,
      onTap: () {
        // Navigate to store details
      },
    );
  }
}
