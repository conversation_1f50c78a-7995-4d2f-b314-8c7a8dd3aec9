import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_dimensions.dart';

class PointsPage extends StatefulWidget {
  const PointsPage({super.key});

  @override
  State<PointsPage> createState() => _PointsPageState();
}

class _PointsPageState extends State<PointsPage> {
  int currentPoints = 1245;
  int availablePoints = 1045;
  int expiredPoints = 200;
  String pointsLevel = 'مستوى ذهبي';
  double pointsValue = 124.5; // ما يعادل $124.5
  int nextLevelPoints = 1500;

  List<Map<String, dynamic>> pointsHistory = [
    {
      'id': '1',
      'type': 'earned',
      'points': 85,
      'description': 'شراء من Nike Store - طلب #NK789',
      'date': '2025/6/28',
      'orderId': '#NK789',
      'icon': Icons.shopping_bag,
      'color': Colors.green,
      'status': 'active',
      'expiryDate': '2025/12/28',
    },
    {
      'id': '2',
      'type': 'spent',
      'points': 50,
      'description': 'خصم على طلب #AD456',
      'date': '2025/6/25',
      'orderId': '#AD456',
      'icon': Icons.discount,
      'color': Colors.red,
      'status': 'used',
      'expiryDate': '',
    },
    {
      'id': '3',
      'type': 'earned',
      'points': 120,
      'description': 'شراء من Adidas Store - طلب #AD456',
      'date': '2025/6/22',
      'orderId': '#AD456',
      'icon': Icons.shopping_cart,
      'color': Colors.green,
      'status': 'active',
      'expiryDate': '2025/12/22',
    },
    {
      'id': '4',
      'type': 'bonus',
      'points': 100,
      'description': 'مكافأة تقييم المنتجات',
      'date': '2025/6/20',
      'orderId': '#BONUS01',
      'icon': Icons.star,
      'color': Colors.orange,
      'status': 'active',
      'expiryDate': '2025/12/20',
    },
    {
      'id': '5',
      'type': 'earned',
      'points': 65,
      'description': 'شراء من Puma Store - طلب #PM123',
      'date': '2025/6/18',
      'orderId': '#PM123',
      'icon': Icons.shopping_bag,
      'color': Colors.green,
      'status': 'active',
      'expiryDate': '2025/12/18',
    },
    {
      'id': '6',
      'type': 'expired',
      'points': 30,
      'description': 'نقاط منتهية الصلاحية',
      'date': '2025/6/15',
      'orderId': '#EXP001',
      'icon': Icons.access_time,
      'color': Colors.grey,
      'status': 'expired',
      'expiryDate': '2025/6/15',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Points Card
            _buildPointsCard(),

            // Points Stats
            _buildPointsStats(),

            const SizedBox(height: 24),

            // Action Buttons
            _buildActionButtons(),

            const SizedBox(height: 24),

            // Points History Section
            _buildPointsHistorySection(),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.primary.withValues(alpha: 0.1), Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
      ),
      title: Text(
        'نقاطي',
        style: GoogleFonts.cairo(
          color: AppColors.textPrimary,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppColors.primary, size: 20),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(Icons.help_outline, color: AppColors.primary, size: 20),
            onPressed: () {
              _showHowToEarnDialog();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPointsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(Icons.stars, color: Colors.white, size: 28),
              Text(
                'نقاطي الإجمالية',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            currentPoints.toString(),
            style: GoogleFonts.robotoMono(
              fontSize: 48,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            pointsLevel,
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'ما يعادل \$${pointsValue.toStringAsFixed(1)}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Progress to next level
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$nextLevelPoints',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                  Text(
                    'للمستوى التالي',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: currentPoints / nextLevelPoints,
                backgroundColor: Colors.white.withValues(alpha: 0.3),
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPointsStats() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'النقاط المتاحة',
              value: availablePoints.toString(),
              icon: Icons.check_circle,
              color: Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              title: 'النقاط المنتهية',
              value: expiredPoints.toString(),
              icon: Icons.access_time,
              color: Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.robotoMono(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                _showHowToEarnDialog();
              },
              icon: const Icon(Icons.lightbulb_outline, size: 20),
              label: Text(
                'كيف أكسب النقاط',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.buttonPaddingVertical,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.buttonBorderRadius,
                  ),
                ),
                elevation: AppDimensions.cardElevation,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                _showPointsHistoryDialog();
              },
              icon: const Icon(Icons.history, size: 20),
              label: Text(
                'عرض السجل الكامل',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.buttonPaddingVertical,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.buttonBorderRadius,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsHistorySection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    _showPointsHistoryDialog();
                  },
                  child: Text(
                    'عرض الكل',
                    style: GoogleFonts.cairo(
                      color: AppColors.primary,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  'آخر المعاملات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: pointsHistory.take(4).length,
            separatorBuilder:
                (context, index) => Divider(
                  height: 1,
                  color: Colors.grey.withValues(alpha: 0.2),
                ),
            itemBuilder: (context, index) {
              final transaction = pointsHistory[index];
              return _buildPointsItem(transaction);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPointsItem(Map<String, dynamic> transaction) {
    final type = transaction['type'] as String;
    final points = transaction['points'] as int;
    final icon = transaction['icon'] as IconData;
    final color = transaction['color'] as Color;

    String prefix = '';
    if (type == 'earned' || type == 'bonus') {
      prefix = '+';
    } else if (type == 'spent') {
      prefix = '-';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),

          const SizedBox(width: 12),

          // Points and Date
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$prefix$points',
                style: GoogleFonts.robotoMono(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                transaction['date'],
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),

          const Spacer(),

          // Description and Order ID
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  transaction['orderId'],
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (transaction['description'].isNotEmpty)
                  Text(
                    transaction['description'],
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.right,
                  ),
                if (transaction['status'] == 'active' &&
                    transaction['expiryDate'].isNotEmpty)
                  Text(
                    'صالح حتى ${transaction['expiryDate']}',
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showPointsHistoryDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'سجل النقاط الكامل',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ListView.separated(
              itemCount: pointsHistory.length,
              separatorBuilder:
                  (context, index) =>
                      Divider(color: Colors.grey.withValues(alpha: 0.2)),
              itemBuilder: (context, index) {
                final transaction = pointsHistory[index];
                return _buildPointsItem(transaction);
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إغلاق',
                style: GoogleFonts.cairo(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showHowToEarnDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'كيف أكسب النقاط؟',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildEarnPointsItem(
                icon: Icons.shopping_cart,
                title: 'الشراء',
                description: 'احصل على نقطة واحدة لكل دولار تنفقه',
                color: Colors.green,
              ),
              const SizedBox(height: 16),
              _buildEarnPointsItem(
                icon: Icons.star,
                title: 'التقييم',
                description: 'احصل على 10 نقاط عند تقييم المنتجات',
                color: Colors.orange,
              ),
              const SizedBox(height: 16),
              _buildEarnPointsItem(
                icon: Icons.share,
                title: 'المشاركة',
                description: 'احصل على 5 نقاط عند مشاركة المنتجات',
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              _buildEarnPointsItem(
                icon: Icons.person_add,
                title: 'دعوة الأصدقاء',
                description: 'احصل على 50 نقطة لكل صديق جديد',
                color: Colors.purple,
              ),
              const SizedBox(height: 16),
              _buildEarnPointsItem(
                icon: Icons.card_giftcard,
                title: 'المناسبات الخاصة',
                description: 'احصل على نقاط إضافية في المناسبات',
                color: Colors.red,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'حسناً',
                style: GoogleFonts.cairo(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEarnPointsItem({
    required IconData icon,
    required String title,
    required String description,
    Color? color,
  }) {
    final iconColor = color ?? AppColors.primary;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: iconColor, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.cairo(
                  fontSize: 13,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.right,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
