import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/routes/app_routes.dart';

class SideMenuDrawer extends StatelessWidget {
  const SideMenuDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: [
          // Header with user profile
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(16, 50, 16, 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    ),
                    const SizedBox(),
                  ],
                ),
                const SizedBox(height: 16),
                const CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.white,
                  child: Icon(Icons.person, size: 40, color: AppColors.primary),
                ),
                const SizedBox(height: 12),
                Text(
                  'مرحباً بك',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'في Sporty Corner',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                const SizedBox(height: 16),

                // Account Section
                _buildSectionHeader('الحساب الشخصي'),
                _buildMenuItem(
                  context,
                  'الملف الشخصي',
                  Icons.person_outline,
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to profile
                  },
                ),
                _buildMenuItem(
                  context,
                  'المفضلة',
                  Icons.favorite_outline,
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to favorites
                  },
                ),
                _buildMenuItem(
                  context,
                  'السلة',
                  Icons.shopping_cart_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to cart
                  },
                ),

                const SizedBox(height: 16),
                _buildDivider(),

                // Categories Section
                _buildSectionHeader('الأقسام'),
                _buildMenuItem(
                  context,
                  'متاجر مميزة',
                  Icons.store_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.featuredStores);
                  },
                ),
                _buildMenuItem(
                  context,
                  'منتجات مميزة',
                  Icons.star_outline,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.featuredProducts);
                  },
                ),
                _buildMenuItem(
                  context,
                  'منتجات الأعلى تقييم',
                  Icons.trending_up,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.topRatedProducts);
                  },
                ),
                _buildMenuItem(
                  context,
                  'منتجات مختارة لك',
                  Icons.recommend_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.selectedProducts);
                  },
                ),
                _buildMenuItem(
                  context,
                  'منتجات خصم أكثر من 40 %',
                  Icons.local_offer_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.discountProducts);
                  },
                ),
                _buildMenuItem(
                  context,
                  'منتجات عامة',
                  Icons.inventory_2_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.generalProducts);
                  },
                ),

                const SizedBox(height: 16),
                _buildDivider(),

                // Services Section
                _buildSectionHeader('الخدمات'),
                _buildMenuItem(
                  context,
                  'قسائم الهدايا الإلكترونية',
                  Icons.card_giftcard_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.giftCardPurchase);
                  },
                ),
                _buildMenuItem(
                  context,
                  'النشرة الإخبارية',
                  Icons.email_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.newsletter);
                  },
                ),

                const SizedBox(height: 16),
                _buildDivider(),

                // Settings & Support Section
                _buildSectionHeader('الإعدادات والدعم'),
                _buildMenuItem(
                  context,
                  'الإعدادات',
                  Icons.settings_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to settings
                  },
                ),
                _buildMenuItem(
                  context,
                  'تواصل معنا',
                  Icons.contact_support_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.contactUs);
                  },
                ),
                _buildMenuItem(
                  context,
                  'من نحن',
                  Icons.info_outline,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.aboutUs);
                  },
                ),
                _buildMenuItem(
                  context,
                  'حول التطبيق',
                  Icons.help_outline,
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog(context);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _buildDivider() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Divider(color: Color(0xFFF0F0F0), thickness: 1),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.transparent,
        ),
        child: Row(
          children: [
            Icon(icon, size: 22, color: AppColors.textSecondary),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            if (onTap != null)
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textSecondary,
              ),
          ],
        ),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'حول التطبيق',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.sports_soccer, size: 60, color: AppColors.primary),
              SizedBox(height: 16),
              Text(
                'Sporty Corner',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'الإصدار 1.0.0',
                style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              Text(
                'تطبيق رياضي شامل لجميع احتياجاتك الرياضية',
                style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'موافق',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
