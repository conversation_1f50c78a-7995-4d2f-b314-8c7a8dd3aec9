import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';

class AppTextStyles {
  // العناوين الرئيسية (Headlines)
  static TextStyle get headlineLarge => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXXXL,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static TextStyle get headlineMedium => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXXL,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static TextStyle get headlineSmall => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXL,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  // عناوين الأقسام (Titles)
  static TextStyle get titleLarge => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static TextStyle get titleMedium => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static TextStyle get titleSmall => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  // النصوص العادية (Body)
  static TextStyle get bodyLarge => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );
  
  static TextStyle get bodyMedium => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );
  
  static TextStyle get bodySmall => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
  );
  
  // نصوص خاصة بالصفحة الرئيسية
  static TextStyle get welcomeText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.w500,
    color: AppColors.primary,
  );
  
  static TextStyle get welcomeUserName => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.w700,
    color: AppColors.primary,
  );
  
  static TextStyle get sectionHeader => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXL,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static TextStyle get viewAllLink => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
    decoration: TextDecoration.none,
  );
  
  // نصوص خاصة بالمنتجات والمتاجر
  static TextStyle get productName => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static TextStyle get storeName => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static TextStyle get priceText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static TextStyle get ratingText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    color: AppColors.textSecondary,
  );
  
  static TextStyle get storeStatus => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    color: AppColors.success,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get clubName => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXS,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
  );
  
  // نصوص خاصة بالأزرار
  static TextStyle get buttonText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeL,
    fontWeight: FontWeight.bold,
    color: AppColors.textOnPrimary,
  );
  
  static TextStyle get buttonTextSecondary => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.w600,
    color: AppColors.primary,
  );
  
  // نصوص خاصة بحقول الإدخال
  static TextStyle get inputText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    color: AppColors.textPrimary,
  );
  
  static TextStyle get hintText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    color: AppColors.textHint,
  );
  
  static TextStyle get labelText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
  );
  
  // نصوص خاصة بشريط التصفح السفلي
  static TextStyle get bottomNavActive => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXS,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static TextStyle get bottomNavInactive => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXS,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
  );
  
  // نصوص خاصة بالحالات
  static TextStyle get successText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.w500,
    color: AppColors.success,
  );
  
  static TextStyle get errorText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.w500,
    color: AppColors.error,
  );
  
  static TextStyle get warningText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.w500,
    color: AppColors.warning,
  );
  
  static TextStyle get infoText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.w500,
    color: AppColors.info,
  );
  
  // نصوص خاصة بالبانر الترويجي
  static TextStyle get bannerTitle => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXXL,
    fontWeight: FontWeight.bold,
    color: AppColors.textOnPrimary,
  );
  
  static TextStyle get bannerSubtitle => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.normal,
    color: AppColors.textOnPrimary,
  );
  
  // نصوص خاصة بالتقييمات والشارات
  static TextStyle get badgeText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXS,
    fontWeight: FontWeight.bold,
    color: AppColors.textOnPrimary,
  );
  
  static TextStyle get captionText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeXS,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
  );
  
  // نصوص خاصة بالروابط
  static TextStyle get linkText => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeM,
    fontWeight: FontWeight.w600,
    color: AppColors.primary,
    decoration: TextDecoration.underline,
  );
  
  static TextStyle get linkTextSmall => GoogleFonts.cairo(
    fontSize: AppDimensions.fontSizeS,
    fontWeight: FontWeight.w600,
    color: AppColors.primary,
    decoration: TextDecoration.underline,
  );
}
