^D:\MY APPS\ABDELLAH APPS\SPORTY_CORNER\BUILD\WINDOWS\X64\CMAKEFILES\399EA641DDA4087E5F84BB345D85311E\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/sporty_corner.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
