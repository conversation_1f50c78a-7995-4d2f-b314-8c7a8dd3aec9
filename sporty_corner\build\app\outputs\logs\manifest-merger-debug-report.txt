-- Merging decision tree log ---
application
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:19
MERGED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db01280b9173f3bec3cbf1bbd68a2bf0\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db01280b9173f3bec3cbf1bbd68a2bf0\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92872a6af2d681548ab51a583a9b8cd9\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92872a6af2d681548ab51a583a9b8cd9\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2928218025dc8a923591c21dfee28c\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2928218025dc8a923591c21dfee28c\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d28d44d06040878891c0ec16b7a65b3\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d28d44d06040878891c0ec16b7a65b3\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c221f25da5a12ffb0e8335918ba8389\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c221f25da5a12ffb0e8335918ba8389\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f28eeabd8b9e9c50efc3c179d74ae60\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f28eeabd8b9e9c50efc3c179d74ae60\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f94746600916a9d0796d680437456a\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f94746600916a9d0796d680437456a\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb954e5e361f5747d9f30518a2d1389a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb954e5e361f5747d9f30518a2d1389a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f41fab3876e230ccded8eebfd5e34cef\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f41fab3876e230ccded8eebfd5e34cef\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\918a396b1429567cf2b526d6f2bb5230\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\918a396b1429567cf2b526d6f2bb5230\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7ea47221d5d15f7d288efddfe52550\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7ea47221d5d15f7d288efddfe52550\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:path_provider_android] D:\My Apps\Abdellah Apps\sporty_corner\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\037a7a4cd91a0025ad140e04b71eaf71\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97eebc2bf02f8e3015d2fa7126673c66\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db01280b9173f3bec3cbf1bbd68a2bf0\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92872a6af2d681548ab51a583a9b8cd9\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2928218025dc8a923591c21dfee28c\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc994e27a4b2cf0a6c05611ed30015a\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f24c396917e95b2a199435591b230fed\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d28d44d06040878891c0ec16b7a65b3\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c221f25da5a12ffb0e8335918ba8389\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f28eeabd8b9e9c50efc3c179d74ae60\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec7e859c7b80f0daf16fcf306e15319\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f94746600916a9d0796d680437456a\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20f0938babcd0b677b1f15b8a35eaaf9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9280c0f7bb24fffeb3e00ebea4db9101\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7a9d5ab9d53e27c63dd2f641f8d8cc4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad80f29221972ebbe6e6fdc10ff87962\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0bb6a9f647754d8fe90c6ab6980a38\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d107bf98a69c826b005b0c1405f065d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19c121f69e3e5a2e116cf736643f3e9a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\398378eb95294e96873a8fb056c1cc23\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12a349fc127cae169e4baf0947f7aaf6\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad71d0d8eabc915499b2f8808ee64c\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1499cb1df04269d3ba203b8ef4093a45\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e51a27e8be75d5de6968e613f53e5cb\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dc922743558d8abefad4b772b73f337\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb954e5e361f5747d9f30518a2d1389a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f41fab3876e230ccded8eebfd5e34cef\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\918a396b1429567cf2b526d6f2bb5230\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acb215a7b18337465da70a2702dd5fbf\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9545c30dcd0af99587d8e68763811df6\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45da90781018b46775a822318eea5004\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c6531ddbb6bcc870a30d7b8a147d49\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3eef6043f41f0f82fab1713778ba15ea\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\368dacea199b2cc36d7ec464c4a425a9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7ea47221d5d15f7d288efddfe52550\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3dbe0c12b75753f0b6efac9b2b4df98\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c4a7f1a93ba78e61ff2f9878b10c707\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b20b99d2a542476aa3a6ab4832b8fb5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e5ad9a6803fbb1f98a470d4c0e12b62\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1429ac18e2b85f5425dfeb99e5c4ee2\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78cc9ee90ae27ecbd79a3f7b4c6353e9\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d67f77aee529040700d07e46c4e1a19\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc2415f04cf10c74fb4ee8a1d8c864f3\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53035d2fc68f77bc991eb2346d4065a7\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\374608de15baf226e2868515e4f04ec8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ced033ec1c3997b2ee3dfa0c2a80cf23\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\My Apps\Abdellah Apps\sporty_corner\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\My Apps\Abdellah Apps\sporty_corner\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\037a7a4cd91a0025ad140e04b71eaf71\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\037a7a4cd91a0025ad140e04b71eaf71\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97eebc2bf02f8e3015d2fa7126673c66\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97eebc2bf02f8e3015d2fa7126673c66\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db01280b9173f3bec3cbf1bbd68a2bf0\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db01280b9173f3bec3cbf1bbd68a2bf0\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92872a6af2d681548ab51a583a9b8cd9\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92872a6af2d681548ab51a583a9b8cd9\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2928218025dc8a923591c21dfee28c\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2928218025dc8a923591c21dfee28c\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc994e27a4b2cf0a6c05611ed30015a\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc994e27a4b2cf0a6c05611ed30015a\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f24c396917e95b2a199435591b230fed\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f24c396917e95b2a199435591b230fed\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d28d44d06040878891c0ec16b7a65b3\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d28d44d06040878891c0ec16b7a65b3\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c221f25da5a12ffb0e8335918ba8389\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c221f25da5a12ffb0e8335918ba8389\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f28eeabd8b9e9c50efc3c179d74ae60\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f28eeabd8b9e9c50efc3c179d74ae60\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec7e859c7b80f0daf16fcf306e15319\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec7e859c7b80f0daf16fcf306e15319\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f94746600916a9d0796d680437456a\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f94746600916a9d0796d680437456a\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20f0938babcd0b677b1f15b8a35eaaf9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20f0938babcd0b677b1f15b8a35eaaf9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9280c0f7bb24fffeb3e00ebea4db9101\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9280c0f7bb24fffeb3e00ebea4db9101\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7a9d5ab9d53e27c63dd2f641f8d8cc4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7a9d5ab9d53e27c63dd2f641f8d8cc4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad80f29221972ebbe6e6fdc10ff87962\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad80f29221972ebbe6e6fdc10ff87962\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0bb6a9f647754d8fe90c6ab6980a38\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0bb6a9f647754d8fe90c6ab6980a38\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d107bf98a69c826b005b0c1405f065d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d107bf98a69c826b005b0c1405f065d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19c121f69e3e5a2e116cf736643f3e9a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19c121f69e3e5a2e116cf736643f3e9a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\398378eb95294e96873a8fb056c1cc23\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\398378eb95294e96873a8fb056c1cc23\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12a349fc127cae169e4baf0947f7aaf6\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12a349fc127cae169e4baf0947f7aaf6\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad71d0d8eabc915499b2f8808ee64c\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad71d0d8eabc915499b2f8808ee64c\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1499cb1df04269d3ba203b8ef4093a45\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1499cb1df04269d3ba203b8ef4093a45\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e51a27e8be75d5de6968e613f53e5cb\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e51a27e8be75d5de6968e613f53e5cb\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dc922743558d8abefad4b772b73f337\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dc922743558d8abefad4b772b73f337\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb954e5e361f5747d9f30518a2d1389a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb954e5e361f5747d9f30518a2d1389a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f41fab3876e230ccded8eebfd5e34cef\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f41fab3876e230ccded8eebfd5e34cef\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\918a396b1429567cf2b526d6f2bb5230\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\918a396b1429567cf2b526d6f2bb5230\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acb215a7b18337465da70a2702dd5fbf\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acb215a7b18337465da70a2702dd5fbf\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9545c30dcd0af99587d8e68763811df6\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9545c30dcd0af99587d8e68763811df6\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45da90781018b46775a822318eea5004\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45da90781018b46775a822318eea5004\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c6531ddbb6bcc870a30d7b8a147d49\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c6531ddbb6bcc870a30d7b8a147d49\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3eef6043f41f0f82fab1713778ba15ea\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3eef6043f41f0f82fab1713778ba15ea\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\368dacea199b2cc36d7ec464c4a425a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\368dacea199b2cc36d7ec464c4a425a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7ea47221d5d15f7d288efddfe52550\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7ea47221d5d15f7d288efddfe52550\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3dbe0c12b75753f0b6efac9b2b4df98\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3dbe0c12b75753f0b6efac9b2b4df98\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c4a7f1a93ba78e61ff2f9878b10c707\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c4a7f1a93ba78e61ff2f9878b10c707\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b20b99d2a542476aa3a6ab4832b8fb5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b20b99d2a542476aa3a6ab4832b8fb5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e5ad9a6803fbb1f98a470d4c0e12b62\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e5ad9a6803fbb1f98a470d4c0e12b62\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1429ac18e2b85f5425dfeb99e5c4ee2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1429ac18e2b85f5425dfeb99e5c4ee2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78cc9ee90ae27ecbd79a3f7b4c6353e9\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78cc9ee90ae27ecbd79a3f7b4c6353e9\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d67f77aee529040700d07e46c4e1a19\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d67f77aee529040700d07e46c4e1a19\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc2415f04cf10c74fb4ee8a1d8c864f3\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc2415f04cf10c74fb4ee8a1d8c864f3\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53035d2fc68f77bc991eb2346d4065a7\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53035d2fc68f77bc991eb2346d4065a7\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\374608de15baf226e2868515e4f04ec8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\374608de15baf226e2868515e4f04ec8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ced033ec1c3997b2ee3dfa0c2a80cf23\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ced033ec1c3997b2ee3dfa0c2a80cf23\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\374608de15baf226e2868515e4f04ec8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\374608de15baf226e2868515e4f04ec8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-65
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:19
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:85
	android:value
		ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-82
	android:name
		ADDED from [:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-128
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0ce1399447847e7d503d3e766e7439\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f007e96d2d3b6eeea782a1ee318defa\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb416ac898e6466ce418dbd1ec305406\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3c9b3bb9f5c6eb454989951ae27efb9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.sporty_corner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.sporty_corner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
