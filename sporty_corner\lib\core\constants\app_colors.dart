import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - ألوان رياضية حديثة
  static const Color primary = Color(0xFF1E88E5); // أزرق رياضي
  static const Color primaryDark = Color(0xFF1565C0);
  static const Color primaryLight = Color(0xFF42A5F5);
  static const Color accentBlue = Color(0xFF60B5FF);

  // Orange Colors - ألوان برتقالية
  static const Color primaryOrange = Color(0xFFFF6B35);
  static const Color secondaryOrange = Color(0xFFFF8E53);
  static const Color accent = Color(0xFFFF7043); // برتقالي حيوي
  static const Color accentLight = Color(0xFFFFAB91);

  // Secondary Colors
  static const Color secondary = Color(0xFF26A69A); // أخضر نعناعي
  static const Color secondaryDark = Color(0xFF00695C);
  static const Color secondaryLight = Color(0xFF4DB6AC);

  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color surfaceColor = Color(0xFFF8F8F8);

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textLight = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textHint = Color(0xFFAAAAAA);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Shadow Colors
  static Color shadowLight = Colors.grey.withValues(alpha: 0.1);
  static Color shadowMedium = Colors.grey.withValues(alpha: 0.2);
  static Color shadowDark = Colors.black.withValues(alpha: 0.1);

  // Transparent Colors
  static Color whiteTransparent = Colors.white.withValues(alpha: 0.9);
  static Color blackTransparent = Colors.black.withValues(alpha: 0.5);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryDark],
  );

  static const LinearGradient orangeGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryOrange, secondaryOrange],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, secondaryDark],
  );
}
