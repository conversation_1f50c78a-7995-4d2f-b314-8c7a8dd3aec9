// Copyright 2023, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

#ifndef PLUGIN_VERSION_CONFIG_H
#define PLUGIN_VERSION_CONFIG_H

namespace firebase_auth_windows {

std::string getPluginVersion() { return "5.6.0"; }
}  // namespace firebase_auth_windows

#endif  // PLUGIN_VERSION_CONFIG_H
