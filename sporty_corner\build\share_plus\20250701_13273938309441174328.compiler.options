"-Xallow-no-source-files" "-classpath" "D:\\My Apps\\Abdellah Apps\\sporty_corner\\build\\share_plus\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78fc21f278f8322fe82a4405e48ea7b7\\transformed\\jetified-flutter_embedding_debug-1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\43fdc0055138604ac2af6f99b404fe22\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5128a4b74de08ebfb0fe9e3c6800f10e\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5fdfa2ed65b60b47f0548c5d5d4ae63d\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a62e18d42120d3821880f3c86363df2\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1554050969f554b536a3d3ab5c6e9428\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bdd48e0f2f3b592466c73c55a05709c3\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\748decc23de1476a1a5570c8dcf39485\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13c6b78cd1c34fc6e1bce3600fb3cb0d\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\31ef0fbaf14a5bc8072f61d630c48386\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\881ad023016dd622d359138c73e0eb4b\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5823689b7d45b3bd99c15879b94e4825\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d77770a8c8fbf5bff0c213829968f9cc\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bc7c90c141b65d75a1deb4a5a7fdc5\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1eb175913602a9e802236a1045fbf07e\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ed3158dffc765444baf9fa1a47a0644\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9a32f9951ae58dc5aba01c9e2f5d0965\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8d33a2ae7377bb35e15cb6798f4b47ec\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f0509b0da26b8682442127955633007\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\549441b379c0fbbd1bf0717aa5814ca5\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\358cb1c51aadfbd2a6d756cdc409b6b2\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7fe70d02978a4d3551f2103f43762e22\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dc7c58e2a5fd07583ac1738bdf64dc79\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21fbe962b49817362e72ef1b6e5809d7\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\82aeeaa5c08add539a957552fbf6b8ab\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f5da170a1c8547745f45bc4a7d5d2f99\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fbb24d06643fd713f733bc911515b759\\transformed\\jetified-kotlin-stdlib-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce2dac7730732efe8f052dc7d7c5373f\\transformed\\jetified-kotlin-stdlib-common-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87817e6103c10ce68799f6d5d284e76a\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\857be8ca9a6dedbb1bd1d97a0ebcd7ff\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\667b32c97a4a21cb89a44a2db9814895\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fd03957f58e1438e25d849d69b897db\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-33\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\My Apps\\Abdellah Apps\\sporty_corner\\build\\share_plus\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "share_plus_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\MethodCallHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\Share.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\ShareFileProvider.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\SharePlusPendingIntent.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\SharePlusPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\ShareSuccessManager.kt"