import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class FilterPage extends StatefulWidget {
  const FilterPage({super.key});

  @override
  State<FilterPage> createState() => _FilterPageState();
}

class _FilterPageState extends State<FilterPage> {
  String selectedSort = 'الأعلى تقييماً';
  String selectedCategory = 'الأقسام';
  String selectedFeature = 'منتجات مميزة';
  String selectedDiscount = '20 %';
  String selectedPrice = '100 - 200';
  double selectedRating = 5.0;
  String selectedCountry = 'عرض الكل';

  // Filter options
  final Map<String, bool> genderFilters = {
    'عرض الكل': false,
    'نسائي': false,
    'رجالي': false,
  };

  final Map<String, bool> sizeFilters = {
    'عرض الكل': false,
    'كبرة قدم': false,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'مرحباً Heba Shaheen',
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: () {},
          ),
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(
                  Icons.notifications_outlined,
                  color: AppColors.textPrimary,
                ),
                onPressed: () {},
              ),
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'ما الذي تبحث عنه ؟',
                  hintStyle: const TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppColors.textSecondary,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ),

          // Filter Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'فرتب حسب',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // Sort Dropdown
                  _buildDropdown(selectedSort, [
                    'الأعلى تقييماً',
                    'الأحدث',
                    'الأقل سعراً',
                    'الأعلى سعراً',
                  ], (value) {
                    setState(() {
                      selectedSort = value!;
                    });
                  }),
                  
                  const SizedBox(height: 24),
                  
                  // Categories
                  const Text(
                    'الأقسام',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  _buildDropdown(selectedCategory, [
                    'الأقسام',
                    'كرة القدم',
                    'كرة السلة',
                    'السباحة',
                    'التنس',
                  ], (value) {
                    setState(() {
                      selectedCategory = value!;
                    });
                  }),
                  
                  const SizedBox(height: 24),
                  
                  // Featured Products
                  const Text(
                    'منتجات مميزة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  _buildDropdown(selectedFeature, [
                    'منتجات مميزة',
                    'عروض خاصة',
                    'الأكثر مبيعاً',
                  ], (value) {
                    setState(() {
                      selectedFeature = value!;
                    });
                  }),
                  
                  const SizedBox(height: 24),
                  
                  // Gender Filters
                  const Text(
                    'عرض',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  _buildCheckboxGroup(genderFilters),
                  
                  const SizedBox(height: 24),
                  
                  // Size Filters
                  _buildCheckboxGroup(sizeFilters),
                  
                  const SizedBox(height: 24),
                  
                  // Discount
                  const Text(
                    'الخصم',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  _buildDropdown(selectedDiscount, [
                    '20 %',
                    '30 %',
                    '50 %',
                    '70 %',
                  ], (value) {
                    setState(() {
                      selectedDiscount = value!;
                    });
                  }),
                  
                  const SizedBox(height: 24),
                  
                  // Price Range
                  const Text(
                    'السعر',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  _buildDropdown(selectedPrice, [
                    '100 - 200',
                    '200 - 500',
                    '500 - 1000',
                    '1000+',
                  ], (value) {
                    setState(() {
                      selectedPrice = value!;
                    });
                  }),
                  
                  const SizedBox(height: 24),
                  
                  // Rating
                  const Text(
                    'التقييم',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE8E8F5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Icon(Icons.keyboard_arrow_down),
                        Row(
                          children: [
                            Text(
                              selectedRating.toString(),
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Country
                  const Text(
                    'الدولة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  _buildDropdown(selectedCountry, [
                    'عرض الكل',
                    'مصر',
                    'السعودية',
                    'الإمارات',
                  ], (value) {
                    setState(() {
                      selectedCountry = value!;
                    });
                  }),
                  
                  const SizedBox(height: 40),
                  
                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            // Reset filters
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: AppColors.primary),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text(
                            'إعادة ضبط',
                            style: TextStyle(
                              color: AppColors.primary,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text(
                            'تطبيق',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(String value, List<String> items, ValueChanged<String?> onChanged) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFFE8E8F5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<String>(
        value: value,
        isExpanded: true,
        underline: const SizedBox(),
        icon: const Icon(Icons.keyboard_arrow_down),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildCheckboxGroup(Map<String, bool> filters) {
    return Column(
      children: filters.entries.map((entry) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              entry.key,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(width: 8),
            Checkbox(
              value: entry.value,
              onChanged: (bool? value) {
                setState(() {
                  filters[entry.key] = value ?? false;
                });
              },
              activeColor: AppColors.primary,
            ),
          ],
        );
      }).toList(),
    );
  }
}
