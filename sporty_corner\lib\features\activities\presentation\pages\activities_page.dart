import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/unified_card.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/routes/app_routes.dart';

class ActivitiesPage extends StatefulWidget {
  const ActivitiesPage({super.key});

  @override
  State<ActivitiesPage> createState() => _ActivitiesPageState();
}

class _ActivitiesPageState extends State<ActivitiesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          AppStrings.activities,
          style: GoogleFonts.cairo(
            color: AppColors.textPrimary,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: AppColors.textPrimary),
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.filter);
            },
          ),
          IconButton(
            icon: const Icon(Icons.search, color: AppColors.textPrimary),
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.search);
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          labelStyle: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'المفضلة'),
            Tab(text: 'أنشطتي'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllActivities(),
          _buildFavoriteActivities(),
          _buildMyActivities(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRoutes.addActivity);
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: AppColors.textOnPrimary),
      ),
    );
  }

  Widget _buildAllActivities() {
    final activities = [
      {
        'title': 'تمرين كرة القدم',
        'location': 'ملعب النادي الأهلي',
        'time': 'اليوم - 16:00',
        'participants': '12/20',
        'icon': Icons.sports_soccer,
        'color': AppColors.primary,
      },
      {
        'title': 'السباحة الحرة',
        'location': 'مسبح الأولمبي',
        'time': 'غداً - 08:00',
        'participants': '8/15',
        'icon': Icons.pool,
        'color': AppColors.secondary,
      },
      {
        'title': 'تمرين كرة السلة',
        'location': 'صالة الرياضة',
        'time': 'غداً - 19:00',
        'participants': '6/10',
        'icon': Icons.sports_basketball,
        'color': AppColors.accent,
      },
      {
        'title': 'جلسة يوغا',
        'location': 'حديقة الأزهر',
        'time': 'الأحد - 07:00',
        'participants': '15/25',
        'icon': Icons.self_improvement,
        'color': AppColors.success,
      },
      {
        'title': 'ماراثون الجري',
        'location': 'كورنيش النيل',
        'time': 'الجمعة - 06:00',
        'participants': '45/100',
        'icon': Icons.directions_run,
        'color': AppColors.warning,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return ActivityCard(
          title: activity['title'] as String,
          location: activity['location'] as String,
          time: activity['time'] as String,
          participants: activity['participants'] as String,
          icon: activity['icon'] as IconData,
          color: activity['color'] as Color,
          onJoin: () {
            // Handle join activity
          },
        );
      },
    );
  }

  Widget _buildFavoriteActivities() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_outline, size: 64, color: AppColors.textLight),
          SizedBox(height: 16),
          Text(
            'لا توجد أنشطة مفضلة',
            style: TextStyle(fontSize: 18, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8),
          Text(
            'اضغط على القلب لإضافة الأنشطة المفضلة',
            style: TextStyle(fontSize: 14, color: AppColors.textLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMyActivities() {
    final myActivities = [
      {
        'title': 'تمرين كرة القدم',
        'status': 'مؤكد',
        'time': 'اليوم - 16:00',
        'icon': Icons.sports_soccer,
        'color': AppColors.success,
      },
      {
        'title': 'السباحة الحرة',
        'status': 'في الانتظار',
        'time': 'غداً - 08:00',
        'icon': Icons.pool,
        'color': AppColors.warning,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: myActivities.length,
      itemBuilder: (context, index) {
        final activity = myActivities[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: (activity['color'] as Color).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                activity['icon'] as IconData,
                color: activity['color'] as Color,
              ),
            ),
            title: Text(
              activity['title'] as String,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  activity['time'] as String,
                  style: const TextStyle(color: AppColors.textSecondary),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: (activity['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    activity['status'] as String,
                    style: TextStyle(
                      fontSize: 12,
                      color: activity['color'] as Color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                    const PopupMenuItem(value: 'cancel', child: Text('إلغاء')),
                  ],
              onSelected: (value) {
                if (value == 'edit') {
                  Navigator.pushNamed(context, AppRoutes.editActivity);
                } else if (value == 'cancel') {
                  // Handle cancel activity
                }
              },
            ),
          ),
        );
      },
    );
  }
}
