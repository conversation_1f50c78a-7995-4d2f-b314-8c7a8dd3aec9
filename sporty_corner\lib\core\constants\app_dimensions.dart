class AppDimensions {
  // المسافات الداخلية (Padding)
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 12.0;
  static const double paddingL = 16.0;
  static const double paddingXL = 20.0;
  static const double paddingXXL = 24.0;
  static const double paddingXXXL = 32.0;
  
  // الهوامش الخارجية (Margin)
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 12.0;
  static const double marginL = 16.0;
  static const double marginXL = 20.0;
  static const double marginXXL = 24.0;
  static const double marginXXXL = 32.0;
  
  // نصف أقطار الحواف (Border Radius)
  static const double radiusXS = 4.0;
  static const double radiusS = 6.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 20.0;
  static const double radiusCircle = 50.0;
  
  // ارتفاعات العناصر (Heights)
  static const double buttonHeight = 48.0;
  static const double inputHeight = 48.0;
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 80.0;
  static const double cardHeight = 250.0;
  static const double productCardHeight = 240.0;
  static const double clubLogoHeight = 120.0;
  static const double bannerHeight = 150.0;
  static const double storeCardImageHeight = 150.0;
  static const double productCardImageHeight = 140.0;
  
  // عروض العناصر (Widths)
  static const double storeCardWidth = 250.0;
  static const double productCardWidth = 180.0;
  static const double clubLogoWidth = 100.0;
  static const double filterButtonWidth = 50.0;
  static const double clubLogoSize = 80.0;
  static const double storeLogoSize = 80.0;
  
  // أحجام الأيقونات (Icon Sizes)
  static const double iconXS = 12.0;
  static const double iconS = 16.0;
  static const double iconM = 20.0;
  static const double iconL = 24.0;
  static const double iconXL = 32.0;
  static const double iconXXL = 40.0;
  
  // أحجام الصور (Image Sizes)
  static const double logoSize = 80.0;
  static const double avatarSize = 40.0;
  static const double notificationBadgeSize = 16.0;
  
  // المسافات الافتراضية للتطبيق
  static const double defaultPadding = paddingL;
  static const double cardBorderRadius = radiusL;
  static const double productCardBorderRadius = radiusM;
  
  // أحجام النصوص (Font Sizes)
  static const double fontSizeXS = 10.0;
  static const double fontSizeS = 12.0;
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXL = 18.0;
  static const double fontSizeXXL = 20.0;
  static const double fontSizeXXXL = 24.0;
  
  // ارتفاعات الأقسام (Section Heights)
  static const double sectionSpacing = 24.0;
  static const double itemSpacing = 16.0;
  static const double smallSpacing = 8.0;
  
  // أبعاد مؤشر الصفحات
  static const double pageIndicatorActiveWidth = 40.0;
  static const double pageIndicatorInactiveWidth = 8.0;
  static const double pageIndicatorHeight = 8.0;
  
  // أبعاد شريط البحث
  static const double searchBarHeight = inputHeight;
  static const double searchBarSpacing = 8.0;
  
  // أبعاد البانر الترويجي
  static const double bannerImageWidth = 120.0;
  static const double bannerContentSpacing = 20.0;
  
  // أبعاد عناصر شريط التصفح السفلي
  static const double bottomNavItemSpacing = 4.0;
  static const double bottomNavIconSize = iconL;
  
  // أبعاد الظلال
  static const double shadowSpreadRadius = 1.0;
  static const double shadowBlurRadiusLight = 4.0;
  static const double shadowBlurRadiusMedium = 8.0;
  static const double shadowOffsetY = 2.0;
  static const double shadowOffsetYHeavy = 4.0;
  static const double shadowOffsetYBottomNav = -2.0;
  
  // أبعاد خاصة بالكروت
  static const double cardElevation = 2.0;
  static const double cardPadding = paddingM;
  static const double cardMargin = paddingL;
  
  // أبعاد خاصة بالأزرار
  static const double buttonPaddingHorizontal = paddingXXL;
  static const double buttonPaddingVertical = paddingM;
  static const double buttonBorderRadius = radiusM;
  
  // أبعاد خاصة بحقول الإدخال
  static const double inputPaddingHorizontal = paddingL;
  static const double inputPaddingVertical = paddingM;
  static const double inputBorderRadius = radiusM;
}
