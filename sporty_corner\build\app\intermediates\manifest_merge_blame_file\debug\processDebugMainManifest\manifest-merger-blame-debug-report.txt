1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.sporty_corner"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
31-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
32-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-65
33    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
33-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
33-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
34    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
34-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
34-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
35-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
35-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
36    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
36-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
36-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
37    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
37-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
37-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5588ccb40a8d391229581aec00f8d274\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
38
39    <permission
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.example.sporty_corner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.example.sporty_corner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44
45    <application
46        android:name="android.app.Application"
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b5141164fa81a8a06c44cbb9b5bf7d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:icon="@mipmap/ic_launcher"
51        android:label="sporty_corner" >
52        <activity
53            android:name="com.example.sporty_corner.MainActivity"
54            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
55            android:exported="true"
56            android:hardwareAccelerated="true"
57            android:launchMode="singleTop"
58            android:taskAffinity=""
59            android:theme="@style/LaunchTheme"
60            android:windowSoftInputMode="adjustResize" >
61
62            <!--
63                 Specifies an Android theme to apply to this Activity as soon as
64                 the Android process has started. This theme is visible to the user
65                 while the Flutter UI initializes. After that, this theme continues
66                 to determine the Window background behind the Flutter UI.
67            -->
68            <meta-data
69                android:name="io.flutter.embedding.android.NormalTheme"
70                android:resource="@style/NormalTheme" />
71
72            <intent-filter>
73                <action android:name="android.intent.action.MAIN" />
74
75                <category android:name="android.intent.category.LAUNCHER" />
76            </intent-filter>
77        </activity>
78        <!--
79             Don't delete the meta-data below.
80             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
81        -->
82        <meta-data
83            android:name="flutterEmbedding"
84            android:value="2" />
85
86        <service
86-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:19
87            android:name="com.google.firebase.components.ComponentDiscoveryService"
87-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:18-89
88            android:directBootAware="true"
88-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
89            android:exported="false" >
89-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
90            <meta-data
90-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:85
91                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
91-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-128
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[:firebase_analytics] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-82
93            <meta-data
93-->[:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
94                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
94-->[:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[:cloud_firestore] D:\My Apps\Abdellah Apps\sporty_corner\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
96            <meta-data
96-->[:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
97                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
97-->[:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[:firebase_auth] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
99            <meta-data
99-->[:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
100                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
100-->[:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[:firebase_storage] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
102            <meta-data
102-->[:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
103                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
103-->[:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[:firebase_core] D:\My Apps\Abdellah Apps\sporty_corner\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
105            <meta-data
105-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
106                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
106-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
108            <meta-data
108-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
109                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
109-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
111            <meta-data
111-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
112                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
112-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac0ce33889e4948df03c62dfb0047c5\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
114            <meta-data
114-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
115                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
115-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
117            <meta-data
117-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
118                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
118-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09b94f705669f3ef2f50d92d01094fa7\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
120            <meta-data
120-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
121                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
121-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af282f6a1936f8f799b425738496dde7\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
123            <meta-data
123-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
124                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
124-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
126            <meta-data
126-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
127                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
127-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d81eacc17006b92042a8e67548a745c6\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
129            <meta-data
129-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
130                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
130-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
132            <meta-data
132-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
133                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
133-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c11e9d7acce9f876df8262c1fee99e0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
135            <meta-data
135-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
136                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
136-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fa66d383314a2aee31c55cac90cdf77\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
138            <meta-data
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
139                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
141        </service>
142        <!--
143           Declares a provider which allows us to store files to share in
144           '.../caches/share_plus' and grant the receiving action access
145        -->
146        <provider
146-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
147            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
147-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
148            android:authorities="com.example.sporty_corner.flutter.share_provider"
148-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
149            android:exported="false"
149-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
150            android:grantUriPermissions="true" >
150-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
151            <meta-data
151-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
152                android:name="android.support.FILE_PROVIDER_PATHS"
152-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
153                android:resource="@xml/flutter_share_file_paths" />
153-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
154        </provider>
155        <!--
156           This manifest declared broadcast receiver allows us to use an explicit
157           Intent when creating a PendingItent to be informed of the user's choice
158        -->
159        <receiver
159-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
160            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
160-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
161            android:exported="false" >
161-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
162            <intent-filter>
162-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
163                <action android:name="EXTRA_CHOSEN_COMPONENT" />
163-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
163-->[:share_plus] D:\My Apps\Abdellah Apps\sporty_corner\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
164            </intent-filter>
165        </receiver>
166
167        <activity
167-->[:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
168            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
168-->[:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
169            android:exported="false"
169-->[:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
170            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
170-->[:url_launcher_android] D:\My Apps\Abdellah Apps\sporty_corner\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
171        <activity
171-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
172            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
172-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
173            android:excludeFromRecents="true"
173-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
174            android:exported="true"
174-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
175            android:launchMode="singleTask"
175-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
176            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
176-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
177            <intent-filter>
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
178                <action android:name="android.intent.action.VIEW" />
178-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
178-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
179
180                <category android:name="android.intent.category.DEFAULT" />
180-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
180-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
181                <category android:name="android.intent.category.BROWSABLE" />
181-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
181-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
182
183                <data
183-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:42:13-50
184                    android:host="firebase.auth"
185                    android:path="/"
186                    android:scheme="genericidp" />
187            </intent-filter>
188        </activity>
189        <activity
189-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
190            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
190-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
191            android:excludeFromRecents="true"
191-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
192            android:exported="true"
192-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
193            android:launchMode="singleTask"
193-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
194-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
195            <intent-filter>
195-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
196                <action android:name="android.intent.action.VIEW" />
196-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
196-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
197
198                <category android:name="android.intent.category.DEFAULT" />
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
199                <category android:name="android.intent.category.BROWSABLE" />
199-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
199-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23c2a9d61fd4ef6fa4ffdd45d466b5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
200
201                <data
201-->D:\My Apps\Abdellah Apps\sporty_corner\android\app\src\main\AndroidManifest.xml:42:13-50
202                    android:host="firebase.auth"
203                    android:path="/"
204                    android:scheme="recaptcha" />
205            </intent-filter>
206        </activity>
207
208        <uses-library
208-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
209            android:name="androidx.window.extensions"
209-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
210            android:required="false" />
210-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
211        <uses-library
211-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
212            android:name="androidx.window.sidecar"
212-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
213            android:required="false" />
213-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a9b6787c023fb1c19432f53eccab067\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
214
215        <provider
215-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
216            android:name="com.google.firebase.provider.FirebaseInitProvider"
216-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
217            android:authorities="com.example.sporty_corner.firebaseinitprovider"
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
218            android:directBootAware="true"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
219            android:exported="false"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
220            android:initOrder="100" />
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\787c29538444fca30f7263b2e5a78798\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
221
222        <receiver
222-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
223            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
223-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
224            android:enabled="true"
224-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
225            android:exported="false" >
225-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
226        </receiver>
227
228        <service
228-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
229            android:name="com.google.android.gms.measurement.AppMeasurementService"
229-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
230            android:enabled="true"
230-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
231            android:exported="false" />
231-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
232        <service
232-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
233            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
233-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
234            android:enabled="true"
234-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
235            android:exported="false"
235-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
236            android:permission="android.permission.BIND_JOB_SERVICE" />
236-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e23be046043c5b7c3de12f2a00c78f4\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
237        <service
237-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
238            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
238-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
239            android:enabled="true"
239-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
240            android:exported="false" >
240-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
241            <meta-data
241-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
242                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
242-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
243                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
243-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
244        </service>
245
246        <activity
246-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
247            android:name="androidx.credentials.playservices.HiddenActivity"
247-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
248            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
248-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
249            android:enabled="true"
249-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
250            android:exported="false"
250-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
251            android:fitsSystemWindows="true"
251-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
252            android:theme="@style/Theme.Hidden" >
252-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60efd76ae3a899d104e279473ba2eb9a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
253        </activity>
254        <activity
254-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
255            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
255-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
256            android:excludeFromRecents="true"
256-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
257            android:exported="false"
257-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
258            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
258-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
259        <!--
260            Service handling Google Sign-In user revocation. For apps that do not integrate with
261            Google Sign-In, this service will never be started.
262        -->
263        <service
263-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
264            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
264-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
265            android:exported="true"
265-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
266            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
266-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
267            android:visibleToInstantApps="true" />
267-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61a223dc9085441ca30cb3e4fa3bf8cc\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
268
269        <provider
269-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
270            android:name="androidx.startup.InitializationProvider"
270-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
271            android:authorities="com.example.sporty_corner.androidx-startup"
271-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
272            android:exported="false" >
272-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
273            <meta-data
273-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
274                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
274-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
275                android:value="androidx.startup" />
275-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3874cf664c486f3a57fb478eadc4204\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
276            <meta-data
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
277                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
278                android:value="androidx.startup" />
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
279        </provider>
280
281        <uses-library
281-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
282            android:name="android.ext.adservices"
282-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
283            android:required="false" />
283-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef624eea6d481b5b010f25500dbf0626\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
284
285        <activity
285-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
286            android:name="com.google.android.gms.common.api.GoogleApiActivity"
286-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
287            android:exported="false"
287-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
288            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
288-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56fc8bcd23ed77b38f9babb141d064b7\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
289
290        <meta-data
290-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
291            android:name="com.google.android.gms.version"
291-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
292            android:value="@integer/google_play_services_version" />
292-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08e3e550d400fc4e42aba4f48929f8e8\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
293
294        <receiver
294-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
295            android:name="androidx.profileinstaller.ProfileInstallReceiver"
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
296            android:directBootAware="false"
296-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
297            android:enabled="true"
297-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
298            android:exported="true"
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
299            android:permission="android.permission.DUMP" >
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
301                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
302            </intent-filter>
303            <intent-filter>
303-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
304                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
305            </intent-filter>
306            <intent-filter>
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
307                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
308            </intent-filter>
309            <intent-filter>
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
310                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60ebdaa378a548a584f47cc17f320a4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
311            </intent-filter>
312        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
313        <activity
313-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
314            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
314-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
315            android:exported="false"
315-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
316            android:stateNotNeeded="true"
316-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
317            android:theme="@style/Theme.PlayCore.Transparent" />
317-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28225ce748584cde03e4c1ef6f64a597\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
318    </application>
319
320</manifest>
