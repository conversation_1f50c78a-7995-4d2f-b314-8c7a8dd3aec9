import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import 'store_detail_page.dart';

class StoresPage extends StatefulWidget {
  final String? clubFilter;
  const StoresPage({super.key, this.clubFilter});

  @override
  State<StoresPage> createState() => _StoresPageState();
}

class _StoresPageState extends State<StoresPage> {
  String selectedFilter = 'الكل';

  @override
  void initState() {
    super.initState();
    if (widget.clubFilter != null) {
      selectedFilter = widget.clubFilter!;
    }
  }
  
  final List<String> filters = [
    'الكل',
    'كرة القدم',
    'كرة السلة',
    'السباحة',
    'التنس',
    'الجري',
    'أحذية',
    'ملابس',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'المتاجر',
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: AppColors.textPrimary),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.filter_list, color: AppColors.textPrimary),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Tabs
          Container(
            height: 50,
            color: Colors.white,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: filters.length,
              itemBuilder: (context, index) {
                final filter = filters[index];
                final isSelected = selectedFilter == filter;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedFilter = filter;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(left: 12, top: 8, bottom: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? AppColors.primary : AppColors.textSecondary,
                      ),
                    ),
                    child: Text(
                      filter,
                      style: TextStyle(
                        color: isSelected ? Colors.white : AppColors.textSecondary,
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Stores List
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Results count
                  Text(
                    'تم العثور على ${_getFilteredStores().length} متجر',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Stores Grid
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 0.85,
                    ),
                    itemCount: _getFilteredStores().length,
                    itemBuilder: (context, index) {
                      final store = _getFilteredStores()[index];
                      return _buildStoreCard(store);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredStores() {
    final allStores = _getAllStores();
    
    if (widget.clubFilter != null) {
      return allStores.where((store) => 
        store['category'] == widget.clubFilter
      ).toList();
    }

    if (selectedFilter == 'الكل') {
      return allStores;
    }
    
    return allStores.where((store) => 
      store['category'] == selectedFilter
    ).toList();
  }

  List<Map<String, dynamic>> _getAllStores() {
    return [
      {
        'id': '1',
        'name': 'SPORTING',
        'logo': 'sporting',
        'rating': 5.0,
        'category': 'كرة القدم',
        'description': 'متجر متخصص في جميع المستلزمات الرياضية الخاصة بكرة القدم',
        'location': 'القاهرة، مصر',
        'isVerified': true,
        'productsCount': 150,
      },
      {
        'id': '2',
        'name': 'Sport Shop',
        'logo': 'sport_shop',
        'rating': 5.0,
        'category': 'أحذية',
        'description': 'متجر Nike متخصص في الأحذية الرياضية',
        'location': 'الجيزة، مصر',
        'isVerified': true,
        'productsCount': 200,
      },
      {
        'id': '3',
        'name': 'SPORTS',
        'logo': 'sports',
        'rating': 4.8,
        'category': 'ملابس',
        'description': 'متجر شامل للملابس الرياضية',
        'location': 'الإسكندرية، مصر',
        'isVerified': true,
        'productsCount': 300,
      },
      {
        'id': '4',
        'name': 'SPORT CLUB',
        'logo': 'sport_club',
        'rating': 4.9,
        'category': 'كرة السلة',
        'description': 'متجر متخصص في معدات كرة السلة',
        'location': 'المنصورة، مصر',
        'isVerified': true,
        'productsCount': 120,
      },
      {
        'id': '5',
        'name': 'Adidas Store',
        'logo': 'adidas',
        'rating': 4.7,
        'category': 'الجري',
        'description': 'متجر أديداس الرسمي',
        'location': 'القاهرة، مصر',
        'isVerified': true,
        'productsCount': 250,
      },
      {
        'id': '6',
        'name': 'Swimming Pro',
        'logo': 'swimming',
        'rating': 4.6,
        'category': 'السباحة',
        'description': 'متجر متخصص في معدات السباحة',
        'location': 'الغردقة، مصر',
        'isVerified': true,
        'productsCount': 80,
      },
    ];
  }

  Widget _buildStoreCard(Map<String, dynamic> store) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => StoreDetailPage(storeName: store['name']),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Store Logo
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: _getStoreColor(store['logo']),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            store['name'].substring(0, 1),
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Verified badge
                    if (store['isVerified'])
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.verified,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    // Share button
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.share,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                    // Favorite button
                    Positioned(
                      bottom: 8,
                      left: 8,
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.favorite_border,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Store Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      store['name'],
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    // Rating
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.star, size: 12, color: Colors.amber),
                        const SizedBox(width: 4),
                        Text(
                          store['rating'].toString(),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    
                    // Products count
                    Text(
                      'متجر Nike',
                      style: const TextStyle(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStoreColor(String logo) {
    switch (logo) {
      case 'sporting':
        return Colors.red;
      case 'sport_shop':
        return Colors.orange;
      case 'sports':
        return Colors.purple;
      case 'sport_club':
        return Colors.blue;
      case 'adidas':
        return Colors.black;
      case 'swimming':
        return Colors.cyan;
      default:
        return AppColors.primary;
    }
  }
}
