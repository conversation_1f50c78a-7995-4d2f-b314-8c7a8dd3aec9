import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/unified_card.dart';
import 'store_detail_page.dart';

class StoresPage extends StatefulWidget {
  final String? clubFilter;
  const StoresPage({super.key, this.clubFilter});

  @override
  State<StoresPage> createState() => _StoresPageState();
}

class _StoresPageState extends State<StoresPage> {
  String selectedFilter = 'الكل';

  @override
  void initState() {
    super.initState();
    if (widget.clubFilter != null) {
      selectedFilter = widget.clubFilter!;
    }
  }

  final List<String> filters = [
    'الكل',
    'كرة القدم',
    'كرة السلة',
    'السباحة',
    'التنس',
    'الجري',
    'أحذية',
    'ملابس',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: Column(
        children: [
          // Filter Tabs
          Container(
            height: 50,
            color: Colors.white,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: filters.length,
              itemBuilder: (context, index) {
                final filter = filters[index];
                final isSelected = selectedFilter == filter;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedFilter = filter;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(left: 12, top: 8, bottom: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? AppColors.primary
                                : AppColors.textSecondary,
                      ),
                    ),
                    child: Text(
                      filter,
                      style: TextStyle(
                        color:
                            isSelected ? Colors.white : AppColors.textSecondary,
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Stores List
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Results count
                  Text(
                    'تم العثور على ${_getFilteredStores().length} متجر',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Stores Grid
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 0.85,
                        ),
                    itemCount: _getFilteredStores().length,
                    itemBuilder: (context, index) {
                      final store = _getFilteredStores()[index];
                      return _buildStoreCard(store);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredStores() {
    final allStores = _getAllStores();

    if (widget.clubFilter != null) {
      return allStores
          .where((store) => store['category'] == widget.clubFilter)
          .toList();
    }

    if (selectedFilter == 'الكل') {
      return allStores;
    }

    return allStores
        .where((store) => store['category'] == selectedFilter)
        .toList();
  }

  List<Map<String, dynamic>> _getAllStores() {
    return [
      {
        'id': '1',
        'name': 'Nike Official Store',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png',
        'rating': 4.9,
        'category': 'كرة القدم',
        'description': 'متجر Nike الرسمي للمنتجات الرياضية',
        'location': 'القاهرة، مصر',
        'isVerified': true,
        'productsCount': 250,
      },
      {
        'id': '2',
        'name': 'Adidas Official',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png',
        'rating': 4.8,
        'category': 'أحذية',
        'description': 'متجر Adidas الرسمي للأحذية والملابس الرياضية',
        'location': 'الجيزة، مصر',
        'isVerified': true,
        'productsCount': 180,
      },
      {
        'id': '3',
        'name': 'Puma Store',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Puma-Logo.png',
        'rating': 4.7,
        'category': 'ملابس',
        'description': 'متجر Puma للأزياء الرياضية العصرية',
        'location': 'الإسكندرية، مصر',
        'isVerified': true,
        'productsCount': 120,
      },
      {
        'id': '4',
        'name': 'Under Armour',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/09/Under-Armour-Logo.png',
        'rating': 4.6,
        'category': 'كرة السلة',
        'description': 'متجر Under Armour للتقنيات الرياضية المتقدمة',
        'location': 'المنصورة، مصر',
        'isVerified': true,
        'productsCount': 95,
      },
      {
        'id': '5',
        'name': 'Reebok Store',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Reebok-Logo.png',
        'rating': 4.5,
        'category': 'الجري',
        'description': 'متجر Reebok للياقة البدنية والجري',
        'location': 'القاهرة، مصر',
        'isVerified': true,
        'productsCount': 85,
      },
      {
        'id': '6',
        'name': 'Speedo Swimming',
        'logo':
            'https://www.speedo.com/dw/image/v2/BDVB_PRD/on/demandware.static/-/Sites-masterCatalog_Speedo/default/dw8c8c8c8c/images/large/speedo-logo.jpg?sw=555&sh=555&sm=fit',
        'rating': 4.4,
        'category': 'السباحة',
        'description': 'متجر Speedo المتخصص في معدات السباحة',
        'location': 'الغردقة، مصر',
        'isVerified': true,
        'productsCount': 60,
      },
    ];
  }

  Widget _buildStoreCard(Map<String, dynamic> store) {
    return StoreCard(
      name: store['name'],
      logo: store['logo'],
      rating: store['rating'].toDouble(),
      description: store['description'],
      productsCount: store['productsCount'],
      isVerified: store['isVerified'],
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => StoreDetailPage(storeName: store['name']),
          ),
        );
      },
    );
  }
}
