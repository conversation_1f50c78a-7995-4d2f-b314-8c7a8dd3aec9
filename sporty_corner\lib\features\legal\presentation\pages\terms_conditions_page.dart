import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class TermsConditionsPage extends StatelessWidget {
  const TermsConditionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'الشروط و الأحكام',
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: Icon<PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.aboutUs);
            },
            child: const Text(
              'حول التطبيق',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            const Text(
              'الشروط و الأحكام',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            _buildTermItem('شروط وأحكام الاستخدام شروط وأحكام الاستخدام'),
            _buildTermItem('شروط وأحكام الاستخدام شروط وأحكام الاستخدام'),
            _buildTermItem('شروط وأحكام الاستخدام شروط وأحكام الاستخدام'),
            _buildTermItem('شروط وأحكام الاستخدام شروط وأحكام الاستخدام'),
            _buildTermItem('شروط وأحكام الاستخدام شروط وأحكام الاستخدام'),
            _buildTermItem('شروط وأحكام الاستخدام.'),

            const SizedBox(height: 16),

            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
            _buildBulletPoint('شروط وأحكام الاستخدام'),
          ],
        ),
      ),
    );
  }

  Widget _buildTermItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: TextDirection.rtl,
        children: [
          const Text(
            '• ',
            style: TextStyle(fontSize: 14, color: AppColors.textPrimary),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
                height: 1.5,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
