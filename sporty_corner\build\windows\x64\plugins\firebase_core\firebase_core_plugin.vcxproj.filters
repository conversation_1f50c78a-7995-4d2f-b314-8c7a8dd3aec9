﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin_c_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include\firebase_core\firebase_core_plugin_c_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated\firebase_core\plugin_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{68F31426-9E92-358E-B61F-D2652B79DF01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{9AC64816-AA02-37CE-8089-E7DF7CEA42FB}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
