^D:\MY APPS\AB<PERSON>LLAH APPS\SPORTY_CORNER\BUILD\WINDOWS\X64\CMAKEFILES\6490C418FC1C772238D86B44280BC1A3\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter "PROJECT_DIR=D:\My Apps\Abdellah Apps\sporty_corner" FLUTTER_ROOT=C:\flutter "FLUTTER_EPHEMERAL_DIR=D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral" "PROJECT_DIR=D:\My Apps\Abdellah Apps\sporty_corner" "FLUTTER_TARGET=D:\My Apps\Abdellah Apps\sporty_corner\lib\main.dart" DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=D:\My Apps\Abdellah Apps\sporty_corner\.dart_tool\package_config.json" C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\MY APPS\ABDELLAH APPS\SPORTY_CORNER\BUILD\WINDOWS\X64\CMAKEFILES\F082877A907F6590E1B5659B9499205C\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\MY APPS\ABDELLAH APPS\SPORTY_CORNER\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
