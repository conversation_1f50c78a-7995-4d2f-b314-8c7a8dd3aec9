import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';

class AppShadows {
  // ظلال خفيفة
  static List<BoxShadow> get light => [
    BoxShadow(
      color: AppColors.shadowLight,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusLight,
      offset: const Offset(0, AppDimensions.shadowOffsetY),
    ),
  ];
  
  // ظلال متوسطة
  static List<BoxShadow> get medium => [
    BoxShadow(
      color: AppColors.shadowMedium,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetY),
    ),
  ];
  
  // ظلال قوية
  static List<BoxShadow> get heavy => [
    BoxShadow(
      color: AppColors.shadowDark,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetYHeavy),
    ),
  ];
  
  // ظل شريط التصفح السفلي
  static List<BoxShadow> get bottomNav => [
    BoxShadow(
      color: AppColors.shadowMedium,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetYBottomNav),
    ),
  ];
  
  // ظل شعارات الأندية
  static List<BoxShadow> get club => [
    BoxShadow(
      color: AppColors.shadowMedium,
      spreadRadius: 2,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetY),
    ),
  ];
  
  // ظل البطاقات
  static List<BoxShadow> get card => [
    BoxShadow(
      color: AppColors.shadowLight,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetY),
    ),
  ];
  
  // ظل الأزرار
  static List<BoxShadow> get button => [
    BoxShadow(
      color: AppColors.shadowLight,
      spreadRadius: 0,
      blurRadius: AppDimensions.shadowBlurRadiusLight,
      offset: const Offset(0, 2),
    ),
  ];
  
  // ظل حقول الإدخال
  static List<BoxShadow> get input => [
    BoxShadow(
      color: AppColors.shadowLight,
      spreadRadius: 0,
      blurRadius: 2,
      offset: const Offset(0, 1),
    ),
  ];
  
  // ظل البانر الترويجي
  static List<BoxShadow> get banner => [
    BoxShadow(
      color: AppColors.shadowDark,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetYHeavy),
    ),
  ];
  
  // ظل شعار المتجر
  static List<BoxShadow> get storeLogo => [
    BoxShadow(
      color: AppColors.shadowDark,
      spreadRadius: AppDimensions.shadowSpreadRadius,
      blurRadius: AppDimensions.shadowBlurRadiusMedium,
      offset: const Offset(0, AppDimensions.shadowOffsetY),
    ),
  ];
  
  // ظل الأيقونات العائمة
  static List<BoxShadow> get floatingIcon => [
    BoxShadow(
      color: AppColors.shadowMedium,
      spreadRadius: 0,
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ];
  
  // ظل القوائم المنسدلة
  static List<BoxShadow> get dropdown => [
    BoxShadow(
      color: AppColors.shadowMedium,
      spreadRadius: 0,
      blurRadius: 8,
      offset: const Offset(0, 4),
    ),
  ];
  
  // ظل النوافذ المنبثقة
  static List<BoxShadow> get modal => [
    BoxShadow(
      color: AppColors.shadowDark,
      spreadRadius: 0,
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];
}
