﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{258F4BFA-456A-3C04-8A4F-6DC1BA79B08D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>cloud_firestore_plugin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cloud_firestore_plugin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cloud_firestore_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">cloud_firestore_plugin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">cloud_firestore_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cloud_firestore_plugin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cloud_firestore_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral;D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/My Apps/Abdellah Apps/sporty_corner/windows/flutter/ephemeral/.plugin_symlinks/cloud_firestore/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/plugins/cloud_firestore/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\plugin_version.h.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/My Apps/Abdellah Apps/sporty_corner/windows/flutter/ephemeral/.plugin_symlinks/cloud_firestore/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/plugins/cloud_firestore/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\plugin_version.h.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/My Apps/Abdellah Apps/sporty_corner/windows/flutter/ephemeral/.plugin_symlinks/cloud_firestore/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/My Apps/Abdellah Apps/sporty_corner/windows" "-BD:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64" --check-stamp-file "D:/My Apps/Abdellah Apps/sporty_corner/build/windows/x64/plugins/cloud_firestore/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\plugin_version.h.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\cloud_firestore\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\include\cloud_firestore\cloud_firestore_plugin_c_api.h" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\cloud_firestore_plugin_c_api.cpp" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\cloud_firestore_plugin.cpp" />
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\cloud_firestore_plugin.h" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\messages.g.cpp" />
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\messages.g.h" />
    <ClCompile Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\firestore_codec.cpp" />
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\windows\flutter\ephemeral\.plugin_symlinks\cloud_firestore\windows\firestore_codec.h" />
    <ClInclude Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\generated\cloud_firestore\plugin_version.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{CB9E5E54-DBB3-340A-B718-3B24DB49E713}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\plugins\firebase_core\firebase_core_plugin.vcxproj">
      <Project>{C784BE17-A647-30F7-A11F-67B5C2AC9115}</Project>
      <Name>firebase_core_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{4A215B23-B748-301A-83D7-788AA1E94AB9}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\My Apps\Abdellah Apps\sporty_corner\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{4926F3B9-348F-323F-83BC-1B219A412E7E}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>