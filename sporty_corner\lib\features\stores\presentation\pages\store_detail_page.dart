import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../core/widgets/unified_card.dart';
import '../../../products/presentation/pages/product_detail_page.dart';

class StoreDetailPage extends StatefulWidget {
  final String storeName;
  const StoreDetailPage({super.key, required this.storeName});

  @override
  State<StoreDetailPage> createState() => _StoreDetailPageState();
}

class _StoreDetailPageState extends State<StoreDetailPage> {
  String selectedCategory = 'الكل';

  @override
  Widget build(BuildContext context) {
    final store = _getStoreDetails();

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        title: Text(
          widget.storeName,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: CustomScrollView(
        slivers: [
          // Store Header
          SliverToBoxAdapter(child: _buildStoreHeader(store)),

          // Categories Filter
          SliverToBoxAdapter(child: _buildCategoriesFilter()),

          // Products Grid
          SliverPadding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: AppDimensions.paddingS,
                mainAxisSpacing: AppDimensions.paddingS,
                childAspectRatio: 0.75,
              ),
              delegate: SliverChildBuilderDelegate((context, index) {
                final products = _getStoreProducts();
                final product = products[index];
                return ProductCard(
                  name: product['name'],
                  image: product['image'],
                  price: '\$${product['price']}',
                  originalPrice:
                      product['originalPrice'] != null
                          ? '\$${product['originalPrice']}'
                          : null,
                  rating: product['rating'].toDouble(),
                  brand: product['brand'],
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                ProductDetailPage(productName: product['name']),
                      ),
                    );
                  },
                );
              }, childCount: _getStoreProducts().length),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreHeader(Map<String, dynamic> store) {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingM),
      child: UnifiedCard(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            // Store Logo
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                child: Image.network(
                  store['logo'],
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(
                      Icons.store,
                      color: AppColors.textSecondary,
                      size: 40,
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: AppDimensions.paddingM),

            // Store Name
            Text(
              store['name'],
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: AppDimensions.paddingS),

            // Store Description
            if (store['description'] != null)
              Text(
                store['description'],
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),

            const SizedBox(height: AppDimensions.paddingM),

            // Store Stats
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatItem(
                  icon: Icons.star,
                  label: 'التقييم',
                  value: store['rating'].toString(),
                  color: Colors.amber,
                ),
                _buildStatItem(
                  icon: Icons.inventory,
                  label: 'المنتجات',
                  value: store['productsCount'].toString(),
                  color: AppColors.primary,
                ),
                if (store['isVerified'])
                  _buildStatItem(
                    icon: Icons.verified,
                    label: 'موثق',
                    value: '✓',
                    color: AppColors.success,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesFilter() {
    final categories = ['الكل', 'أحذية', 'ملابس', 'إكسسوارات', 'معدات'];

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategory == category;

          return Container(
            margin: const EdgeInsets.only(left: AppDimensions.paddingS),
            child: FilterChip(
              label: Text(
                category,
                style: GoogleFonts.cairo(
                  color:
                      isSelected
                          ? AppColors.textOnPrimary
                          : AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  selectedCategory = category;
                });
              },
              backgroundColor: AppColors.surface,
              selectedColor: AppColors.primary,
              checkmarkColor: AppColors.textOnPrimary,
              side: BorderSide(
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
            ),
          );
        },
      ),
    );
  }

  Map<String, dynamic> _getStoreDetails() {
    // البحث عن المتجر بالاسم
    final stores = [
      {
        'name': 'Nike Official Store',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png',
        'rating': 4.9,
        'description': 'متجر Nike الرسمي للمنتجات الرياضية عالية الجودة',
        'productsCount': 250,
        'isVerified': true,
      },
      {
        'name': 'Adidas Official',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png',
        'rating': 4.8,
        'description': 'متجر Adidas الرسمي للأحذية والملابس الرياضية',
        'productsCount': 180,
        'isVerified': true,
      },
      {
        'name': 'Under Armour',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/09/Under-Armour-Logo.png',
        'rating': 4.5,
        'description': 'متجر Under Armour للتقنيات الرياضية المتقدمة',
        'productsCount': 95,
        'isVerified': true,
      },
      {
        'name': 'Reebok Store',
        'logo':
            'https://logos-world.net/wp-content/uploads/2020/04/Reebok-Logo.png',
        'rating': 4.4,
        'description': 'متجر Reebok للياقة البدنية والتدريب',
        'productsCount': 85,
        'isVerified': true,
      },
    ];

    return stores.firstWhere(
      (store) => store['name'] == widget.storeName,
      orElse: () => stores.first,
    );
  }

  List<Map<String, dynamic>> _getStoreProducts() {
    // منتجات حسب المتجر
    final allProducts = {
      'Nike Official Store': [
        {
          'name': 'حذاء Nike Air Max 270',
          'image':
              'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/awjogtdnqxniqqk0wpgf/air-max-270-shoes-KkLcGR.png',
          'price': 150,
          'originalPrice': 180,
          'rating': 4.8,
          'brand': 'Nike',
          'category': 'أحذية',
        },
        {
          'name': 'قميص Nike Dri-FIT',
          'image':
              'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/16a6a8cd-db80-4ec4-9c7e-6e3e8c3b8c3e/dri-fit-adv-techknit-ultra-mens-short-sleeve-running-top-VlJkNV.png',
          'price': 45,
          'originalPrice': null,
          'rating': 4.6,
          'brand': 'Nike',
          'category': 'ملابس',
        },
        {
          'name': 'شورت Nike Pro',
          'image':
              'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/7b5b8c8e-8c8e-4c8e-8c8e-8c8e8c8e8c8e/pro-mens-dri-fit-shorts-6-inch-VlJkNV.png',
          'price': 35,
          'originalPrice': 45,
          'rating': 4.5,
          'brand': 'Nike',
          'category': 'ملابس',
        },
        {
          'name': 'حقيبة Nike Brasilia',
          'image':
              'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/brasilia-training-duffel-bag-medium-9-0-VlJkNV.png',
          'price': 25,
          'originalPrice': null,
          'rating': 4.4,
          'brand': 'Nike',
          'category': 'إكسسوارات',
        },
      ],
      'Adidas Official': [
        {
          'name': 'حذاء Adidas Ultraboost 22',
          'image':
              'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a78bc4896a3e9ad7800abcec6_9366/Ultraboost_22_Shoes_Black_GZ0127_01_standard.jpg',
          'price': 180,
          'originalPrice': 200,
          'rating': 4.9,
          'brand': 'Adidas',
          'category': 'أحذية',
        },
        {
          'name': 'قميص Adidas Tiro 21',
          'image':
              'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/a8d8c7b8c8d8e8f8g8h8i8j8k8l8m8n8o8p8q8r8s8t8u8v8w8x8y8z8/tiro-21-training-jersey-blue-GM7374_01_laydown.jpg',
          'price': 40,
          'originalPrice': null,
          'rating': 4.7,
          'brand': 'Adidas',
          'category': 'ملابس',
        },
      ],
      'Under Armour': [
        {
          'name': 'حذاء Under Armour HOVR',
          'image':
              'https://underarmour.scene7.com/is/image/Underarmour/3023550-001_DEFAULT?rp=standard-30pad|pdpMainDesktop&scl=1&fmt=jpg&qlt=85&resMode=sharp2&cache=on,on&bgc=f0f0f0&wid=566&hei=708',
          'price': 120,
          'originalPrice': 140,
          'rating': 4.6,
          'brand': 'Under Armour',
          'category': 'أحذية',
        },
      ],
      'Reebok Store': [
        {
          'name': 'حذاء Reebok Classic',
          'image':
              'https://assets.reebok.com/images/h_840,f_auto,q_auto:sensitive,fl_lossy,c_fill,g_auto/a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6/classic-leather-shoes-white-49799_01_standard.jpg',
          'price': 75,
          'originalPrice': 90,
          'rating': 4.3,
          'brand': 'Reebok',
          'category': 'أحذية',
        },
      ],
    };

    final storeProducts = allProducts[widget.storeName] ?? [];

    // تطبيق فلتر الفئة
    if (selectedCategory == 'الكل') {
      return storeProducts;
    } else {
      return storeProducts
          .where((product) => product['category'] == selectedCategory)
          .toList();
    }
  }
}
