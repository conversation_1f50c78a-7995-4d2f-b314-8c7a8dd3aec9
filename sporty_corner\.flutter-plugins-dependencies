{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-5.6.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_analytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics-11.5.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.6.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.14.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_storage-12.4.7\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-5.6.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_analytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics-11.5.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.6.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.14.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_storage-12.4.7\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.3.16\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-5.6.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_analytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics-11.5.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.6.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.14.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_storage-12.4.7\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-5.6.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-5.6.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.14.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_storage-12.4.7\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "cloud_firestore_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore_web-4.4.9\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics_web-0.5.10+13\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth_web-5.15.0\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.23.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_storage_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_storage_web-3.10.14\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.4.1\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_storage", "dependencies": ["firebase_core", "firebase_storage_web"]}, {"name": "firebase_storage_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-07-01 13:22:07.872470", "version": "3.29.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}