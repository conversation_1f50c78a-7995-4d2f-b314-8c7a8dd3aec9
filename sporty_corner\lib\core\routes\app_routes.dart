import 'package:flutter/material.dart';
import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/settings/presentation/pages/account_settings_page.dart';
import '../../features/settings/presentation/pages/app_settings_page.dart';
import '../../features/settings/presentation/pages/change_password_page.dart';
import '../../features/settings/presentation/pages/contact_us_page.dart';
import '../../features/gift_card/presentation/pages/gift_card_purchase_page.dart';
import '../../features/gift_card/presentation/pages/gift_card_success_page.dart';
import '../../features/stores/presentation/pages/stores_page.dart';
import '../../features/products/presentation/pages/products_list_page.dart';
import '../../features/newsletter/presentation/pages/newsletter_page.dart';
import '../../features/about/presentation/pages/about_us_page.dart';
import '../../features/legal/presentation/pages/privacy_policy_page.dart';
import '../../features/legal/presentation/pages/terms_conditions_page.dart';
import '../../features/notifications/presentation/pages/notifications_page.dart';
import '../../features/cart/presentation/pages/cart_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/favorites/presentation/pages/favorites_page.dart';
import '../../features/search/presentation/pages/filter_page.dart';
import '../../features/search/presentation/pages/search_page.dart'; // Assuming a search page exists or will be created
import '../../features/activities/presentation/pages/add_activity_page.dart';
import '../../features/activities/presentation/pages/edit_activity_page.dart';
import '../../features/checkout/presentation/pages/checkout_page.dart';
import '../../features/order/presentation/pages/order_success_page.dart';
import '../../features/contact/presentation/pages/call_us_page.dart';
import '../../features/payment_methods/presentation/pages/add_payment_method_page.dart';
import '../../features/profile/presentation/pages/edit_profile_page.dart';
import '../../features/profile/presentation/pages/edit_address_page.dart';
import '../../features/order/presentation/pages/orders_page.dart';
import '../../features/order/presentation/pages/order_details_page.dart';
import '../../features/payment_methods/presentation/pages/payment_methods_page.dart';
import '../../features/points/presentation/pages/points_page.dart';
import '../../features/wallet/presentation/pages/wallet_page.dart';

import '../../features/products/presentation/pages/product_detail_page.dart';
import '../../features/stores/presentation/pages/store_detail_page.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String accountSettings = '/account-settings';
  static const String appSettings = '/app-settings';
  static const String changePassword = '/change-password';
  static const String contactUs = '/contact-us';
  static const String giftCardPurchase = '/gift-card-purchase';
  static const String giftCardSuccess = '/gift-card-success';
  static const String stores = '/stores';
  static const String featuredStores = '/featured-stores';
  static const String featuredProducts = '/featured-products';
  static const String topRatedProducts = '/top-rated-products';
  static const String selectedProducts = '/selected-products';
  static const String discountProducts = '/discount-products';
  static const String generalProducts = '/general-products';
  static const String newsletter = '/newsletter';
  static const String aboutUs = '/about-us';
  static const String notifications = '/notifications';
  static const String cart = '/cart';
  static const String profile = '/profile';
  static const String favorites = '/favorites';
  static const String filter = '/filter';
  static const String search = '/search';
  static const String addActivity = '/add-activity';
  static const String editActivity = '/edit-activity';
  static const String checkout = '/checkout';
  static const String orderSuccess = '/order-success';
  static const String callUs = '/call-us';
  static const String addPaymentMethod = '/add-payment-method';
  static const String editPaymentMethod = '/edit-payment-method';
  static const String editProfile = '/edit-profile';
  static const String editAddress = '/edit-address';
  static const String orders = '/orders';
  static const String orderDetails = '/order-details';
  static const String paymentMethods = '/payment-methods';
  static const String points = '/points';
  static const String wallet = '/wallet';
  static const String productDetail = '/product-detail';
  static const String storeDetail = '/store-detail';
  static const String clubProducts = '/club-products';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsConditions = '/terms-conditions';

  static Map<String, WidgetBuilder> get routes {
    return {
      splash: (context) => const SplashPage(),
      login: (context) => const LoginPage(),
      register: (context) => const RegisterPage(),
      forgotPassword: (context) => const ForgotPasswordPage(),
      home: (context) => const HomePage(),
      accountSettings: (context) => const AccountSettingsPage(),
      appSettings: (context) => const AppSettingsPage(),
      changePassword: (context) => const ChangePasswordPage(),
      contactUs: (context) => const ContactUsPage(),
      giftCardPurchase: (context) => const GiftCardPurchasePage(),
      giftCardSuccess: (context) => const GiftCardSuccessPage(),
      stores: (context) => const StoresPage(),
      featuredStores: (context) => const StoresPage(),
      featuredProducts:
          (context) =>
              const ProductsPage(category: 'featured', title: 'منتجات مميزة'),
      topRatedProducts:
          (context) => const ProductsPage(
            category: 'top-rated',
            title: 'منتجات الأعلى تقييماً',
          ),
      selectedProducts:
          (context) => const ProductsPage(
            category: 'selected',
            title: 'منتجات مختارة لك',
          ),
      discountProducts:
          (context) =>
              const ProductsPage(category: 'discount', title: 'منتجات خصم'),
      generalProducts:
          (context) =>
              const ProductsPage(category: 'general', title: 'المنتجات العامة'),
      newsletter: (context) => const NewsletterPage(),
      aboutUs: (context) => const AboutUsPage(),
      notifications: (context) => const NotificationsPage(),
      cart: (context) => const CartPage(),
      profile: (context) => const ProfilePage(),
      favorites: (context) => const FavoritesPage(),
      filter: (context) => const FilterPage(),
      search: (context) => const SearchPage(),
      addActivity: (context) => const AddActivityPage(),
      editActivity: (context) => const EditActivityPage(),
      checkout:
          (context) => CheckoutPage(
            cartItems: [],
            total: 0.0,
          ), // Placeholder, will be updated
      orderSuccess:
          (context) => OrderSuccessPage(
            orderNumber: '',
            total: 0.0,
          ), // Placeholder, will be updated
      callUs: (context) => const CallUsPage(),
      addPaymentMethod: (context) => const AddPaymentMethodPage(),
      editPaymentMethod:
          (context) =>
              const AddPaymentMethodPage(), // Reusing for edit, pass arguments
      editProfile: (context) => const EditProfilePage(),
      editAddress: (context) => const EditAddressPage(),
      orders: (context) => const OrdersPage(),
      paymentMethods: (context) => const PaymentMethodsPage(),
      points: (context) => const PointsPage(),
      wallet: (context) => const WalletPage(),
      privacyPolicy: (context) => const PrivacyPolicyPage(),
      termsConditions: (context) => const TermsConditionsPage(),
    };
  }

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case productDetail:
        return MaterialPageRoute(
          builder:
              (context) =>
                  ProductDetailPage(productName: settings.arguments as String),
        );
      case storeDetail:
        return MaterialPageRoute(
          builder:
              (context) =>
                  StoreDetailPage(storeName: settings.arguments as String),
        );
      case clubProducts:
        return MaterialPageRoute(
          builder:
              (context) => ProductsPage(
                category: 'club',
                title: 'منتجات ${settings.arguments as String}',
              ),
        );
      case orderDetails:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (context) => OrderDetailsPage(
                orderId: args['orderId'],
                orderStatus: args['orderStatus'],
              ),
        );
      default:
        final builder = routes[settings.name];
        if (builder != null) {
          return MaterialPageRoute(builder: builder, settings: settings);
        }
        return null;
    }
  }
}
