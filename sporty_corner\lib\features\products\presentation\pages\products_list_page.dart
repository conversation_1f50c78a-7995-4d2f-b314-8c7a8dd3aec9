import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/unified_card.dart';
import '../../../search/presentation/pages/smart_search_page.dart';
import 'product_detail_page.dart';

class ProductsPage extends StatefulWidget {
  final String category;
  final String title;

  const ProductsPage({super.key, required this.category, required this.title});

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  String selectedFilter = 'الكل';

  final List<String> filters = [
    'الكل',
    'كرة القدم',
    'كرة السلة',
    'السباحة',
    'التنس',
    'الجري',
    'أحذية',
    'ملابس',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.title,
          style: GoogleFonts.cairo(
            color: AppColors.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.search,
                color: AppColors.textPrimary,
                size: 22,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SmartSearchPage(),
                  ),
                );
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list, color: AppColors.textPrimary),
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.filter);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Tabs
          Container(
            height: 50,
            color: Colors.white,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: filters.length,
              itemBuilder: (context, index) {
                final filter = filters[index];
                final isSelected = selectedFilter == filter;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedFilter = filter;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(left: 12, top: 8, bottom: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? AppColors.primary
                                : AppColors.textSecondary,
                      ),
                    ),
                    child: Text(
                      filter,
                      style: TextStyle(
                        color:
                            isSelected ? Colors.white : AppColors.textSecondary,
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Products Grid
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Results count
                  Text(
                    'تم العثور على ${_getFilteredProducts().length} منتج',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Products Grid
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 0.75,
                        ),
                    itemCount: _getFilteredProducts().length,
                    itemBuilder: (context, index) {
                      final product = _getFilteredProducts()[index];
                      return _buildProductCard(product);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredProducts() {
    final allProducts = _getAllProducts();

    // تحويل فئة الصفحة من الإنجليزية إلى العربية
    String arabicCategory = _getArabicCategory(widget.category);

    // إذا كانت الفئة خاصة بنادي معين
    if (widget.category == 'club' && widget.title.contains('منتجات ')) {
      String clubName = widget.title.replaceFirst('منتجات ', '');
      if (selectedFilter == 'الكل') {
        return allProducts
            .where(
              (product) =>
                  product['category'] == 'منتجات النادي' &&
                  product['club'] == clubName,
            )
            .toList();
      } else {
        return allProducts
            .where(
              (product) =>
                  product['category'] == 'منتجات النادي' &&
                  product['club'] == clubName &&
                  product['sportType'] == selectedFilter,
            )
            .toList();
      }
    }

    if (selectedFilter == 'الكل') {
      return allProducts
          .where(
            (product) =>
                product['category'] == arabicCategory ||
                widget.category == 'الكل',
          )
          .toList();
    }

    return allProducts
        .where(
          (product) =>
              product['sportType'] == selectedFilter &&
              (product['category'] == arabicCategory ||
                  widget.category == 'الكل'),
        )
        .toList();
  }

  String _getArabicCategory(String englishCategory) {
    switch (englishCategory) {
      case 'featured':
        return 'منتجات مميزة';
      case 'top-rated':
        return 'منتجات الأعلى تقييماً';
      case 'selected':
        return 'منتجات مختارة لك';
      case 'discount':
        return 'منتجات خصم';
      case 'general':
        return 'منتجات عامة';
      case 'club':
        return 'منتجات النادي';
      default:
        return 'الكل';
    }
  }

  List<Map<String, dynamic>> _getAllProducts() {
    return [
      {
        'id': '1',
        'title': 'كرة قدم Adidas UCL Pro',
        'price': '45.00',
        'originalPrice': '55.00',
        'rating': 4.9,
        'image':
            'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8dbf2fcdf1b54b5b9a05af4d00826e47_9366/UCL_Pro_Ball_White_HT2486_01_standard.jpg',
        'category': 'منتجات عامة',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Adidas',
      },
      {
        'id': '2',
        'title': 'حذاء Nike Air Max 270',
        'price': '120.00',
        'originalPrice': '150.00',
        'rating': 4.8,
        'image':
            'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff3-46b4-949b-2d16af2ad421/custom-nike-dunk-high-by-you-shoes.png',
        'category': 'منتجات مميزة',
        'sportType': 'أحذية',
        'hasDiscount': true,
        'brand': 'Nike',
      },
      {
        'id': '3',
        'title': 'مضرب Wilson Pro Staff',
        'price': '85.00',
        'originalPrice': '100.00',
        'rating': 4.3,
        'image':
            'https://www.wilson.com/medias/WR038611U-1.jpg?context=bWFzdGVyfGltYWdlc3w0NzQ4NnxpbWFnZS9qcGVnfGltYWdlcy9oNGMvaGE4LzEwOTc4NzQ4NzIzMjMwLmpwZ3w1YzQzNjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4Zg',
        'category': 'منتجات الأعلى تقييماً',
        'sportType': 'التنس',
        'hasDiscount': true,
        'brand': 'Wilson',
      },
      {
        'id': '4',
        'title': 'كرة سلة Spalding NBA',
        'price': '30.00',
        'originalPrice': '40.00',
        'rating': 4.6,
        'image':
            'https://www.spalding.com/dw/image/v2/ABQL_PRD/on/demandware.static/-/Sites-masterCatalog_Spalding/default/dw8c8c8c8c/images/large/29.5_NBA_Official_Game_Ball_83-043Z_A.jpg?sw=555&sh=555&sm=fit',
        'category': 'منتجات مختارة لك',
        'sportType': 'كرة السلة',
        'hasDiscount': true,
        'brand': 'Spalding',
      },
      {
        'id': '5',
        'title': 'بدلة رياضية Adidas Tiro',
        'price': '45.00',
        'originalPrice': '60.00',
        'rating': 4.4,
        'image':
            'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/a8b8c8d8e8f8g8h8i8j8k8l8m8n8o8p8q8r8s8t8u8v8w8x8y8z8/tiro-track-suit.jpg',
        'category': 'منتجات خصم',
        'sportType': 'ملابس',
        'hasDiscount': true,
        'brand': 'Adidas',
      },
      {
        'id': '6',
        'title': 'نظارات سباحة Speedo',
        'price': '15.00',
        'originalPrice': '20.00',
        'rating': 4.2,
        'image':
            'https://www.speedo.com/dw/image/v2/BDVB_PRD/on/demandware.static/-/Sites-masterCatalog_Speedo/default/dw8c8c8c8c/images/large/8-12139C299_1.jpg?sw=555&sh=555&sm=fit',
        'category': 'منتجات عامة',
        'sportType': 'السباحة',
        'hasDiscount': true,
        'brand': 'Speedo',
      },
      {
        'id': '7',
        'title': 'حذاء جري Nike Air Zoom',
        'price': '95.00',
        'originalPrice': '120.00',
        'rating': 4.7,
        'image':
            'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b8b8c8d8e8f8g8h8i8j8k8l8m8n8o8p8q8r8s8t8u8v8w8x8y8z8/air-zoom-pegasus-39-running-shoe.png',
        'category': 'منتجات مميزة',
        'sportType': 'الجري',
        'hasDiscount': true,
        'brand': 'Nike',
      },
      {
        'id': '8',
        'title': 'قميص ريال مدريد الرسمي',
        'price': '75.00',
        'originalPrice': '85.00',
        'rating': 4.8,
        'image':
            'https://shop.realmadrid.com/_next/image?url=https%3A%2F%2Flegends.broadleafcloud.com%2Fapi%2Fasset%2Fcontent%2FRMCFMZ0165-01.jpg%3FcontextRequest%3D%257B%2522forceCatalogForFetch%2522%3Afalse%2C%2522applicationId%2522%3A%252201H4RD9NXMKQBQ1WVKM1181VD8%2522%2C%2522tenantId%2522%3A%2522REAL_MADRID%2522%257D&w=1920&q=75',
        'category': 'منتجات خصم',
        'sportType': 'ملابس',
        'hasDiscount': true,
        'brand': 'Real Madrid',
      },
      // منتجات مميزة إضافية
      {
        'id': '9',
        'title': 'حذاء Adidas Ultraboost 22',
        'price': '180.00',
        'originalPrice': '200.00',
        'rating': 4.9,
        'image':
            'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a78bc4896a3e9ad7800abcec6_9366/Ultraboost_22_Shoes_Black_GZ0127_01_standard.jpg',
        'category': 'منتجات مميزة',
        'sportType': 'أحذية',
        'hasDiscount': true,
        'brand': 'Adidas',
      },
      {
        'id': '10',
        'title': 'Apple Watch Nike Series 8',
        'price': '399.00',
        'originalPrice': '429.00',
        'rating': 4.8,
        'image':
            'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/MNUL3ref_VW_34FR+watch-45-alum-midnight-nc-8s_VW_34FR_WF_CO?wid=1400&hei=1400&trim=1%2C0&fmt=p-jpg&qlt=95&.v=1661370042471%2C1661971866219',
        'category': 'منتجات مميزة',
        'sportType': 'أحذية',
        'hasDiscount': true,
        'brand': 'Apple',
      },
      // منتجات أعلى تقييماً إضافية
      {
        'id': '11',
        'title': 'مضرب تنس Babolat Pure Drive',
        'price': '199.00',
        'originalPrice': '229.00',
        'rating': 4.9,
        'image':
            'https://www.babolat.com/sites/default/files/styles/product_page_main_image/public/2021-07/101334-136-PURE-DRIVE-2021.png?itok=8c8c8c8c',
        'category': 'منتجات الأعلى تقييماً',
        'sportType': 'التنس',
        'hasDiscount': true,
        'brand': 'Babolat',
      },
      {
        'id': '12',
        'title': 'كرة قدم Nike Flight',
        'price': '35.00',
        'originalPrice': '45.00',
        'rating': 4.7,
        'image':
            'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c/flight-soccer-ball.png',
        'category': 'منتجات الأعلى تقييماً',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Nike',
      },
      // منتجات مختارة لك إضافية
      {
        'id': '13',
        'title': 'حقيبة رياضية Under Armour',
        'price': '65.00',
        'originalPrice': '80.00',
        'rating': 4.5,
        'image':
            'https://underarmour.scene7.com/is/image/Underarmour/1342651-001_FC?rp=standard-0pad|pdpMainDesktop&scl=1&fmt=jpg&qlt=85&resMode=sharp2&cache=on,on&bgc=f0f0f0&wid=566&hei=708',
        'category': 'منتجات مختارة لك',
        'sportType': 'أحذية',
        'hasDiscount': true,
        'brand': 'Under Armour',
      },
      {
        'id': '14',
        'title': 'قفازات ملاكمة Everlast',
        'price': '45.00',
        'originalPrice': '60.00',
        'rating': 4.4,
        'image':
            'https://www.everlast.com/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/1/2/1200014_red_1.jpg',
        'category': 'منتجات مختارة لك',
        'sportType': 'الملاكمة',
        'hasDiscount': true,
        'brand': 'Everlast',
      },
      // منتجات خصم إضافية
      {
        'id': '15',
        'title': 'شورت رياضي Nike Dri-FIT',
        'price': '25.00',
        'originalPrice': '40.00',
        'rating': 4.3,
        'image':
            'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c/dri-fit-shorts.png',
        'category': 'منتجات خصم',
        'sportType': 'ملابس',
        'hasDiscount': true,
        'brand': 'Nike',
      },
      {
        'id': '16',
        'title': 'حذاء Puma Future Z',
        'price': '89.00',
        'originalPrice': '120.00',
        'rating': 4.2,
        'image':
            'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/106363/01/sv01/fnd/PNA/fmt/png/FUTURE-Z-1.2-FG/AG-Men\'s-Football-Boots',
        'category': 'منتجات خصم',
        'sportType': 'أحذية',
        'hasDiscount': true,
        'brand': 'Puma',
      },
      // منتجات عامة إضافية
      {
        'id': '17',
        'title': 'زجاجة مياه رياضية',
        'price': '12.00',
        'originalPrice': '15.00',
        'rating': 4.1,
        'image':
            'https://m.media-amazon.com/images/I/61QZ8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c.jpg',
        'category': 'منتجات عامة',
        'sportType': 'أحذية',
        'hasDiscount': true,
        'brand': 'Generic',
      },
      {
        'id': '18',
        'title': 'حصيرة يوغا',
        'price': '20.00',
        'originalPrice': '25.00',
        'rating': 4.0,
        'image':
            'https://m.media-amazon.com/images/I/71QZ8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c.jpg',
        'category': 'منتجات عامة',
        'sportType': 'اليوغا',
        'hasDiscount': true,
        'brand': 'Generic',
      },
      // منتجات الأندية
      {
        'id': '21',
        'title': 'قميص Real Madrid الرسمي',
        'price': '89.99',
        'originalPrice': '99.99',
        'rating': 4.9,
        'image':
            'https://store.realmadrid.com/images/products/RM1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Adidas',
        'club': 'Real Madrid',
      },
      {
        'id': '22',
        'title': 'قميص Barcelona الرسمي',
        'price': '85.99',
        'originalPrice': '95.99',
        'rating': 4.8,
        'image':
            'https://store.fcbarcelona.com/images/products/FCB1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Nike',
        'club': 'FC Barcelona',
      },
      {
        'id': '23',
        'title': 'قميص Manchester United',
        'price': '87.99',
        'originalPrice': '97.99',
        'rating': 4.7,
        'image':
            'https://images.footballfanatics.com/manchester-united/manchester-united-home-shirt-2023-24_ss4_p-13371779+u-9jz2q8z8z8z8z8z8z8z8+v-c8c8c8c8c8c8c8c8.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'TeamViewer',
        'club': 'Manchester United',
      },
      {
        'id': '24',
        'title': 'قميص Liverpool الرسمي',
        'price': '83.99',
        'originalPrice': '93.99',
        'rating': 4.8,
        'image':
            'https://store.liverpoolfc.com/images/products/LFC1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Nike',
        'club': 'Liverpool FC',
      },
      {
        'id': '25',
        'title': 'قميص Chelsea الرسمي',
        'price': '86.99',
        'originalPrice': '96.99',
        'rating': 4.6,
        'image':
            'https://images.chelseafc.com/images/products/CFC1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Nike',
        'club': 'Chelsea FC',
      },
      {
        'id': '26',
        'title': 'قميص Arsenal FC الرسمي',
        'price': '84.99',
        'originalPrice': '94.99',
        'rating': 4.7,
        'image':
            'https://images.arsenal.com/images/products/AFC1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Adidas',
        'club': 'Arsenal FC',
      },
      {
        'id': '27',
        'title': 'قميص Bayern Munich الرسمي',
        'price': '88.99',
        'originalPrice': '98.99',
        'rating': 4.9,
        'image':
            'https://store.fcbayern.com/images/products/FCB1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Adidas',
        'club': 'Bayern Munich',
      },
      {
        'id': '28',
        'title': 'قميص PSG الرسمي',
        'price': '89.99',
        'originalPrice': '99.99',
        'rating': 4.8,
        'image': 'https://store.psg.fr/images/products/PSG1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Nike',
        'club': 'Paris Saint-Germain',
      },
      {
        'id': '29',
        'title': 'قميص Juventus الرسمي',
        'price': '87.99',
        'originalPrice': '97.99',
        'rating': 4.6,
        'image':
            'https://store.juventus.com/images/products/JUV1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Adidas',
        'club': 'Juventus FC',
      },
      {
        'id': '30',
        'title': 'قميص Manchester City الرسمي',
        'price': '88.99',
        'originalPrice': '98.99',
        'rating': 4.8,
        'image':
            'https://shop.mancity.com/images/products/MCFC1M2023H01_01.jpg',
        'category': 'منتجات النادي',
        'sportType': 'كرة القدم',
        'hasDiscount': true,
        'brand': 'Puma',
        'club': 'Manchester City',
      },
    ];
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return ProductCard(
      name: product['title'],
      image: product['image'],
      price: '\$${product['price']}',
      originalPrice:
          product['originalPrice'] != null
              ? '\$${product['originalPrice']}'
              : null,
      rating: product['rating'].toDouble(),
      brand: product['brand'],
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ProductDetailPage(productName: product['title']),
          ),
        );
      },
    );
  }
}
